<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\HotelRoomCategory;
use App\Models\HotelMeal;
use App\Models\HotelRoomRate;
use App\Models\QuotationHotelConfirmation;

class HotelService
{
  public static function getHotelSummeryData($quotation)
  {
    $hotelData = [];
    $previous = null;
    foreach ($quotation['days'] ?? [] as $dayData) {
      if (isset($dayData['hotels'])) {
        if ($previous !== $dayData['hotels']['hotel-id']) {
          $hotel = \App\Models\Hotel::with(['cityR', 'hotelClass'])->find($dayData['hotels']['hotel-id']);
          $cityName = $hotel->cityR ? $hotel->cityR->name : 'No City Assigned';
          $hotelCatName = $hotel->hotelClass ? $hotel->hotelClass->class : 'No Hotel Class Assigned';
          $mealType = \App\Models\HotelMeal::find($dayData['hotels']['meal']);
          $roomCategory = \App\Models\HotelRoomCategory::find($dayData['hotels']['room_category']);
          $processRoomData = function ($data) {
            $roomsArray = [];

            if ($data['hotels']['single'] != "0") {
              $roomsArray[] = $data['hotels']['single'] . " SGL";
            }

            if ($data['hotels']['double'] != "0") {
              $roomsArray[] = $data['hotels']['double'] . " DBL";
            }

            if ($data['hotels']['triple'] != "0") {
              $roomsArray[] = $data['hotels']['triple'] . " TRP";
            }

            if ($data['hotels']['quadruple'] != "0") {
              $roomsArray[] = $data['hotels']['quadruple'] . " QUA";
            }

            return $roomsArray;
          };

          $formattedHotel = [
            'hotel_id' => $hotel->id,
            'hotel_name' => $hotel->name ?? 'Unknown Hotel',
            'city_name' => $cityName,
            'hotel_category' => $hotelCatName,
            'checkin_date' => date('M d, Y', strtotime($dayData['date'])),
            'checkout_date' => HotelService::getCheckoutDate($dayData['date'], $dayData['hotels']['nights']),
            'nights' => $dayData['hotels']['nights'] . " " . __('labels.nights'),
            'rooms_count' => $processRoomData($dayData),
            'room_category' => $roomCategory->name,
            'meal_type' => $mealType->type,
          ];


          $hotelData[] = $formattedHotel;
        }
        $previous = $dayData['hotels']['hotel-id'];
      }
    }

    return $hotelData;
  }

  public static function getHotelStatusData($quotation_id)
  {
      // Fetch hotel confirmation statuses for the given quotation ID
      $hotelStatuses = QuotationHotelConfirmation::where('quotation_id', $quotation_id)
          ->pluck('status', 'hotel_id') // Returns an associative array: [hotel_id => status]
          ->toArray();

      return $hotelStatuses;
  }



  public static function getCheckoutDate($checkInDate, $nights, $format = 'M d, Y')
  {
    // Check if checkInDate is valid and not 'N/A' or empty
    if (empty($checkInDate) || $checkInDate === 'N/A' || $checkInDate === null) {
      return 'N/A';
    }

    try {
      $checkInDateTime = Carbon::parse($checkInDate);
      $checkoutDate = $checkInDateTime->addDays($nights);

      return $checkoutDate->format($format);
    } catch (\Exception $e) {
      // If parsing fails, return 'N/A' instead of throwing an error
      return 'N/A';
    }
  }

  /**
   * Get hotel rates based on the provided parameters.
   *
   * @param int $hotel
   * @param string $date
   * @param int $meal
   * @param int $room_type
   * @param int $room_category
   * @param int $market
   * @return HotelRoomRate|null
   */
  public function getHotelRates($hotel, $date = null, $meal = 1, $room_types = [2], $room_category = 9, $market = 1)
  {
    $date = $date ?? Carbon::now()->format('Y-m-d');

    $rates = HotelRoomRate::with(['mealR', 'hotelR', 'roomType', 'roomCategory', 'marketR'])
      ->where('hotel', $hotel)
      ->where('start_date', '<=', $date)
      ->where('end_date', '>', $date)
      ->where('meal', $meal)
      ->whereIn('room_type', $room_types)
      ->where('room_category', $room_category)
      ->where('market', $market)
      ->get();

    // If no rates found, create default rates with 0 values
    if ($rates->isEmpty()) {
      $defaultRates = [];
      foreach ($room_types as $room_type) {
        $defaultRate = new HotelRoomRate();
        $defaultRate->hotel = $hotel;
        $defaultRate->meal = $meal;
        $defaultRate->room_type = $room_type;
        $defaultRate->room_category = $room_category;
        $defaultRate->market = $market;
        $defaultRate->adult_rate = 0;
        $defaultRate->cwb_rate = 0;
        $defaultRate->cnb_rate = 0;
        $defaultRates[] = $defaultRate;
      }
      return collect($defaultRates);
    }

    return $rates;
  }

  public function getAvailableRoomCategories($hotelId, $date = null)
  {
    $date = $date ?? Carbon::now()->format('Y-m-d');

    $availableRoomCategories = HotelRoomCategory::whereHas('hotelRoomRates', function ($query) use ($hotelId, $date) {
      $query->where('hotel', $hotelId)
        ->where('start_date', '<=', $date)
        ->where('end_date', '>=', $date);
    })->get();

    if ($availableRoomCategories->isEmpty()) {
      return HotelRoomCategory::all(); // Return all categories if none are found
    }

    return $availableRoomCategories;
  }

  public function getAvailableMealTypes($hotelId, $date = null)
  {
    $date = $date ?? Carbon::now()->format('Y-m-d');

    $availableMeals = HotelMeal::whereHas('hotelRoomRates', function ($query) use ($hotelId, $date) {
      $query->where('hotel', $hotelId)
        ->where('start_date', '<=', $date)
        ->where('end_date', '>=', $date);
    })->get();

    if ($availableMeals->isEmpty()) {
      return HotelMeal::all(); // Return all meal types if none are found
    }

    return $availableMeals;
  }

  function getRoomTypeStructure($quotation, $day, $fromAlgorithm = false)
  {
    $roomTypes = [
      '1' => 0, // For 1 person
      '2' => 0, // For 2 persons
      '3' => 0, // For 3 persons
      '4' => 0, // For 4 persons
    ];

    if ($fromAlgorithm) {
      return self::algoRoomTypeStructure($quotation);
    } else if (isset($quotation['days'][$day]) && isset($quotation['days'][$day]['hotels'])) {
      $roomTypes = [
        '1' => (isset($quotation['days'][$day]['hotels']['single']) && $quotation['days'][$day]['hotels']['single'] > 0) ? $quotation['days'][$day]['hotels']['single'] : 0, // For 1 person
        '2' => (isset($quotation['days'][$day]['hotels']['double']) && $quotation['days'][$day]['hotels']['double'] > 0) ? $quotation['days'][$day]['hotels']['double'] : 0,  // For 2 persons
        '3' => (isset($quotation['days'][$day]['hotels']['triple']) && $quotation['days'][$day]['hotels']['triple'] > 0) ? $quotation['days'][$day]['hotels']['triple'] : 0,  // For 3 persons
        '4' => (isset($quotation['days'][$day]['hotels']['quadruple']) && $quotation['days'][$day]['hotels']['quadruple'] > 0) ? $quotation['days'][$day]['hotels']['quadruple'] : 0,  // For 4 persons
      ];
    } else {
      return self::algoRoomTypeStructure($quotation);
    }

    return $roomTypes;
  }

  static function algoRoomTypeStructure($quotation)
  {
      $roomTypes = [
          '1' => 0, // For 1 person
          '2' => 0, // For 2 persons
          '3' => 0, // For 3 persons
          '4' => 0, // For 4 persons (Quadruple)
      ];

      $pax = QuotationService::categorizeGuests($quotation);
      $pax = $pax['adults'];

      // Prioritize triple rooms first
      $roomTypes['3'] = intdiv($pax, 3); // Max number of quadruple rooms
      $remainingPax = $pax % 3; // Remaining people after assigning quadruple rooms

      // Assign double rooms if possible
      if ($remainingPax >= 2) {
          $roomTypes['2'] = 1;
          $remainingPax -= 2;
      }

      // Assign single room if one person remains
      if ($remainingPax === 1) {
          $roomTypes['1'] = 1;
      }

      return $roomTypes;
  }

  function calculateHotelRates($quotation)
  {
    // Initialize the room rates and total to zero
    $totalRate = 0;
    $singleRoomRate = 0;
    $doubleRoomRate = 0;
    $tripleRoomRate = 0;
    $quadrupleRoomRate = 0;
    $cwbRate = 0;
    $cnbRate = 0;

    // Initialize markup multipliers
    $adultMarkupMultiplier = 1;
    $childMarkupMultiplier = 1;

    if (isset($quotation['markup_type']) && $quotation['markup_type'] == '1') {
      $adultMarkupMultiplier = 1 + ($quotation['adult_markup'] / 100);
      $childMarkupMultiplier = 1 + ($quotation['child_markup'] / 100);
    }

    $lastKey = array_key_last($quotation['days']);
    // Loop through the days in the quotation
    foreach ($quotation['days'] as $key => $dayData) {
      if (isset($dayData['hotels'])) {
        if($dayData['hotels']['own-arrangement']) {
            if ($key === $lastKey) continue;
        }
        $hotelData = $dayData['hotels'];

        // Calculate the rates for the single room
        if (isset($hotelData['single']) && $hotelData['single'] > 0 && isset($hotelData['adult_rate[1]'])) {
          $singleRoomRate += add_markup((double)$hotelData['adult_rate[1]'] / 1, $adultMarkupMultiplier); // Single room rate per person
          $totalRate += add_markup(((double)$hotelData['adult_rate[1]'] / 1) * $hotelData['single'], $adultMarkupMultiplier); // Multiply by the number of single rooms
        }

        // Calculate the rates for the double room
        if (isset($hotelData['double']) && $hotelData['double'] > 0 && isset($hotelData['adult_rate[2]'])) {
          $doubleRoomRate += add_markup((double)$hotelData['adult_rate[2]'] / 2, $adultMarkupMultiplier); // Double room rate per person
          $totalRate += add_markup(((double)$hotelData['adult_rate[2]'] / 2) * $hotelData['double'] * 2, $adultMarkupMultiplier); // Multiply by the number of double rooms and 2
        }

        // Calculate the rates for the triple room
        if (isset($hotelData['triple']) && $hotelData['triple'] > 0 && isset($hotelData['adult_rate[3]'])) {
          $tripleRoomRate += add_markup((double)$hotelData['adult_rate[3]'] / 3, $adultMarkupMultiplier); // Triple room rate per person
          $totalRate += add_markup(((double)$hotelData['adult_rate[3]'] / 3) * $hotelData['triple'] * 3, $adultMarkupMultiplier); // Multiply by the number of triple rooms and 3
        }

        // Calculate the rates for the quadruple room
        if (isset($hotelData['quadruple']) && $hotelData['quadruple'] > 0 && isset($hotelData['adult_rate[4]'])) {
          $quadrupleRoomRate += add_markup((double)$hotelData['adult_rate[4]'] / 4, $adultMarkupMultiplier); // Quadruple room rate per person
          $totalRate += add_markup(((double)$hotelData['adult_rate[4]'] / 4) * $hotelData['quadruple'] * 4, $adultMarkupMultiplier); // Multiply by the number of quadruple rooms and 4
        }

        // Add CWB and CNB rates
        if (isset($hotelData['cwb_rate'])) {
          $cwbRate += add_markup($hotelData['cwb_rate'], $childMarkupMultiplier); // Add CWB rate
          $totalRate += add_markup((double)$hotelData['cwb_rate'] * QuotationService::categorizeGuests($quotation)['cwb'], $childMarkupMultiplier);
        }
        if (isset($hotelData['cnb_rate'])) {
          $cnbRate += add_markup($hotelData['cnb_rate'], $childMarkupMultiplier); // Add CNB rate
          $totalRate += add_markup((double)$hotelData['cnb_rate'] * QuotationService::categorizeGuests($quotation)['cnb'], $childMarkupMultiplier);
        }
      }
    }

    return [
      'single' => $singleRoomRate,
      'double' => $doubleRoomRate,
      'triple' => $tripleRoomRate,
      'quadruple' => $quadrupleRoomRate,
      'cwb' => $cwbRate,
      'cnb' => $cnbRate,
      'total' => $totalRate
    ];
  }

  public static function getHotelPNLData($quotation)
  {
    $hotelData = [];
    $total = 0;
    $totalRoomRate = 0;
    $totalChildRate = 0;
    $groupedHotelData = [];
    $lastKey = array_key_last($quotation['days']);

    // Add discounted rates tracking
    $discountedRates = [];
    $discountTotal = 0;


    foreach ($quotation['days'] ?? [] as $key => $dayData) {
      if($dayData['hotels']['own-arrangement']) {
          if ($key === $lastKey) continue;
      }
      if (isset($dayData['hotels'])) {
        $hotel = $dayData['hotels'];
        $hotelId = $hotel['hotel-id'];

        // Add data to grouped array by hotel
        if (!isset($groupedHotelData['data'][$hotelId])) {
          $totalRoomRate = 0;
          $totalChildRate = 0;
          $discountTotal = 0;

          $hotelData = \App\Models\Hotel::with([
            'cityR',
            'contacts' => function ($query) {
              $query->where('type', 2);
            }
          ])->find($hotelId);
          $contacts = $hotelData->contacts->pluck('contact_id')->toArray();;
          $cityName = $hotelData->cityR ? $hotelData->cityR->name : 'No City Assigned';
          $mealType = \App\Models\HotelMeal::find($hotel['meal']);
          $roomCategory = \App\Models\HotelRoomCategory::find($hotel['room_category']);
          $groupedHotelData['data'][$hotelId] = [
            'hotel_name' => $hotelData->name ?? 'Unknown Hotel', // Example names
            'city_name' => $cityName,
            'contacts' => $contacts,
            'checkin_date' => date('M d, Y', strtotime($dayData['date'])),
            'checkout_date' => HotelService::getCheckoutDate($dayData['date'], $hotel['nights']),
            'nights' =>  ($dayData['hotels']['nights'] == 1) ? $dayData['hotels']['nights'] . ' night' :  $dayData['hotels']['nights'] . ' nights',
            'room_category' => $roomCategory->name,
            'meal_type' => $mealType->type,
            'room_arrangement' => [],
            'child_rates' => [],
            'day' => $key,
            'discount_title' => $hotel['discount_title'] ?? 'Discount given by Hotel',
          ];
        }

        // Add room arrangements
        $date = date("Y/m/d", strtotime($dayData['date']));

        // Calculate single room rate and add to total
        if ($hotel['single'] > 0 && isset($hotel['adult_rate[1]'])) {
          $roomTotal = $hotel['single'] * $hotel['adult_rate[1]'] ?? 0; // Single room total
          $discountedTotal = $roomTotal;

          if(isset($hotel['discounted_adult_rate[1]'])) {
              $discountedTotal = $hotel['single'] * $hotel['discounted_adult_rate[1]'] ?? 0; // Single room total
              // Track the discount
              $discountedRates['SNL'] = $hotel['discounted_adult_rate[1]'];
              $discountTotal += ($discountedTotal);
          }

          $span = "<span class='voucher-hotel-rate1' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='single' data-day='{$key}'> {$hotel['adult_rate[1]']} </span>";
          if(isset($hotel['discounted_adult_rate[1]'])) {
              //$span = "<span class='voucher-hotel-rate1 text-decoration-line-through' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='single' data-day='{$key}'> {$hotel['adult_rate[1]']} </span><span>{$hotel['discounted_adult_rate[1]']}</span>";
          }
          $groupedHotelData['data'][$hotelId]['room_arrangement'][] = "{$date} SNL : {$hotel['single']} x {$span} = {$roomTotal}";
          $groupedHotelData['data'][$hotelId]['discounted_rates']['SNL'] = $hotel['discounted_adult_rate[1]'] ?? 0;
          $totalRoomRate += $roomTotal;
        }

        // Calculate double room rate and add to total
        if ($hotel['double'] > 0 && isset($hotel['adult_rate[2]'])) {
          $roomTotal = $hotel['double'] * $hotel['adult_rate[2]'] ?? 0; // Double room total
          $discountedTotal = $roomTotal;

          if(isset($hotel['discounted_adult_rate[2]'])) {
              $discountedTotal = $hotel['double'] * $hotel['discounted_adult_rate[2]'] ?? 0; // Double room total
              // Track the discount
              $discountedRates['DBL'] = $hotel['discounted_adult_rate[2]'];
              $discountTotal += ($discountedTotal);
          }

          $span = "<span class='voucher-hotel-rate1' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='double' data-day='{$key}'> {$hotel['adult_rate[2]']} </span>";
          if(isset($hotel['discounted_adult_rate[2]'])) {
              //$span = "<span class='voucher-hotel-rate1 text-decoration-line-through' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='double' data-day='{$key}'> {$hotel['adult_rate[2]']} </span><span>{$hotel['discounted_adult_rate[2]']}</span>";
          }
          $groupedHotelData['data'][$hotelId]['room_arrangement'][] = "{$date} DBL : {$hotel['double']} x {$span} = {$roomTotal}";
          $groupedHotelData['data'][$hotelId]['discounted_rates']['DBL'] = $hotel['discounted_adult_rate[2]'] ?? 0;
          $totalRoomRate += $roomTotal;
        }

        // Calculate triple room rate and add to total
        if ($hotel['triple'] > 0 && isset($hotel['adult_rate[3]'])) {
          $roomTotal = $hotel['triple'] * $hotel['adult_rate[3]'] ?? 0; // Triple room total
          $discountedTotal = $roomTotal;

          if(isset($hotel['discounted_adult_rate[3]'])) {
              $discountedTotal = $hotel['triple'] * $hotel['discounted_adult_rate[3]'] ?? 0; // Triple room total
              // Track the discount
              $discountedRates['TPL'] = $hotel['discounted_adult_rate[3]'];
              $discountTotal += ($discountedTotal);
          }

          $span = "<span class='voucher-hotel-rate1' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='triple' data-day='{$key}'> {$hotel['adult_rate[3]']} </span>";
          if(isset($hotel['discounted_adult_rate[3]'])) {
              //$span = "<span class='voucher-hotel-rate1 text-decoration-line-through' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='triple' data-day='{$key}'> {$hotel['adult_rate[3]']} </span><span>{$hotel['discounted_adult_rate[3]']}</span>";
          }
          $groupedHotelData['data'][$hotelId]['room_arrangement'][] = "{$date} TPL : {$hotel['triple']} x {$span} = {$roomTotal}";
          $groupedHotelData['data'][$hotelId]['discounted_rates']['TPL'] = $hotel['discounted_adult_rate[3]'] ?? 0;
          $totalRoomRate += $roomTotal;
        }

        // Calculate triple room rate and add to total
        if ($hotel['quadruple'] > 0 && isset($hotel['adult_rate[4]'])) {
          $roomTotal = $hotel['quadruple'] * $hotel['adult_rate[4]'] ?? 0; // Triple room total
          $discountedTotal = $roomTotal;

          if(isset($hotel['discounted_adult_rate[4]'])) {
              $discountedTotal = $hotel['quadruple'] * $hotel['discounted_adult_rate[4]'] ?? 0; // Triple room total
              // Track the discount
              $discountedRates['QUA'] = $hotel['discounted_adult_rate[4]'];
              $discountTotal += ($discountedTotal);
          }

          $span = "<span class='voucher-hotel-rate1' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='quadruple' data-day='{$key}'> {$hotel['adult_rate[4]']} </span>";
          if(isset($hotel['discounted_adult_rate[4]'])) {
              //$span = "<span class='voucher-hotel-rate1 text-decoration-line-through' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='quadruple' data-day='{$key}'> {$hotel['adult_rate[4]']} </span><span>{$hotel['discounted_adult_rate[4]']}</span>";
          }
          $groupedHotelData['data'][$hotelId]['room_arrangement'][] = "{$date} QUA : {$hotel['quadruple']} x {$span} = {$roomTotal}";
          $groupedHotelData['data'][$hotelId]['discounted_rates']['QUA'] = $hotel['discounted_adult_rate[4]'] ?? 0;
          $totalRoomRate += $roomTotal;
        }

        // Add child rates and calculate totals
        if ($hotel['cwb_rate'] > 0) {
          $childTotal = QuotationService::categorizeGuests($quotation)['cwb'] * $hotel['cwb_rate']; // CWB total
          $groupedHotelData['data'][$hotelId]['child_rates'][] = "{$date} CWB : " . QuotationService::categorizeGuests($quotation)['cwb'] . " x <span class='voucher-hotel-rate1' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='cwb' data-day='{$key}'> {$hotel['cwb_rate']} </span> = {$childTotal}";
          $totalChildRate += $childTotal;
        }
        if ($hotel['cnb_rate'] > 0) {
          $childTotal = QuotationService::categorizeGuests($quotation)['cnb'] * $hotel['cnb_rate']; // CNB total
          $groupedHotelData['data'][$hotelId]['child_rates'][] = "{$date} CNB : " . QuotationService::categorizeGuests($quotation)['cnb'] . " x <span class='voucher-hotel-rate1' data-quotation-id='{$quotation['id']}' data-hotel-id='{$hotelId}' data-room-type='cnb' data-day='{$key}'> {$hotel['cnb_rate']} </span> = {$childTotal}";
          $totalChildRate += $childTotal;
        }

        // Store the totals for each hotel
        $groupedHotelData['data'][$hotelId]['total_room_rate'] = $totalRoomRate;
        $groupedHotelData['data'][$hotelId]['total_child_rate'] = $totalChildRate;
        $groupedHotelData['data'][$hotelId]['total_rate'] = $totalRoomRate + $totalChildRate - $discountTotal;
        $groupedHotelData['data'][$hotelId]['discount_total'] = $discountTotal;
      }
    }
    $groupedHotelData['total'] = array_reduce($groupedHotelData['data'] ?? [], function ($carry, $item) {
      return $carry + $item['total_rate'];
    }, 0);

    // Add discount information
    if (!empty($discountedRates)) {
      $groupedHotelData['discounted_rates'] = $discountedRates;
      $groupedHotelData['discount_total'] = $discountTotal;
    }

    return $groupedHotelData;
  }

  public static function getHotelsList($quotation)
  {
    $hotelData = [];
    $previous = null;
    foreach ($quotation['days'] ?? [] as $dayData) {
      if (isset($dayData['hotels'])) {
        if ($previous !== $dayData['hotels']['hotel-id']) {
          $hotelData[] = $dayData['hotels']['hotel-id'];
        }
        $previous = $dayData['hotels']['hotel-id'];
      }
    }

    return $hotelData;
  }
}
