<?php

namespace App\Services;

use App\Models\Attraction;
use App\Models\CityTour;
use App\Models\Excursion;


class AttractionService
{
  function calculateAttractionRates($quotation)
  {
    // Initialize the room rates and total to zero
    $totalRate = 0;
    $singleRate = 0;
    $doubleRate = 0;
    $tripleRate = 0;
    $quadrupleRate = 0;
    $cwbRate = 0;
    $cnbRate = 0;

    // Initialize markup multipliers
    $adultMarkupMultiplier = 1;
    $childMarkupMultiplier = 1;

    if(isset($quotation['markup_type']) && $quotation['markup_type'] == '1') {
        $adultMarkupMultiplier = 1 + ($quotation['adult_markup'] / 100);
        $childMarkupMultiplier = 1 + ($quotation['child_markup'] / 100);
    }

    // Loop through the days in the quotation
    foreach ($quotation['days'] as $key => $dayData) {
      if (isset($dayData['attractions']) && !empty($dayData['attractions'])) {
        foreach ($dayData['attractions'] as $attractionData) {
          if (isset($quotation['days'][1]['hotels']['single']) && $quotation['days'][1]['hotels']['single'] > 0) {
            $singleRate += add_markup($attractionData['adult_rate'], $adultMarkupMultiplier);
            $totalRate += add_markup((double)$attractionData['adult_rate'] * (double)$quotation['days'][1]['hotels']['single'] * 1, $adultMarkupMultiplier); // Multiply by the number of single rooms
          }

          if (isset($quotation['days'][1]['hotels']['double']) && $quotation['days'][1]['hotels']['double'] > 0) {
            $doubleRate += add_markup($attractionData['adult_rate'], $adultMarkupMultiplier);
            $totalRate += add_markup((double)$attractionData['adult_rate'] * (double)$quotation['days'][1]['hotels']['double'] * 2, $adultMarkupMultiplier); // Multiply by the number of single rooms
          }

          if (isset($quotation['days'][1]['hotels']['triple']) && $quotation['days'][1]['hotels']['triple'] > 0) {
            $tripleRate += add_markup($attractionData['adult_rate'], $adultMarkupMultiplier);
            $totalRate += add_markup((double)$attractionData['adult_rate'] * (double)$quotation['days'][1]['hotels']['triple'] * 3, $adultMarkupMultiplier); // Multiply by the number of single rooms
          }

          if (isset($quotation['days'][1]['hotels']['quadruple']) && $quotation['days'][1]['hotels']['quadruple'] > 0) {
            $quadrupleRate += add_markup($attractionData['adult_rate'], $adultMarkupMultiplier);
            $totalRate += add_markup((double)$attractionData['adult_rate'] * (double)$quotation['days'][1]['hotels']['quadruple'] * 4, $adultMarkupMultiplier); // Multiply by the number of single rooms
          }

          if (QuotationService::categorizeGuests($quotation)['cwb'] > 0) {
            $cwbRate += add_markup($attractionData['child_rate'], $childMarkupMultiplier);
            $totalRate += add_markup((double)$attractionData['child_rate'] * QuotationService::categorizeGuests($quotation)['cwb'], $childMarkupMultiplier);
          }

          if (QuotationService::categorizeGuests($quotation)['cnb'] > 0) {
            $cnbRate += add_markup($attractionData['child_rate'], $childMarkupMultiplier);
            $totalRate += add_markup((double)$attractionData['child_rate'] * QuotationService::categorizeGuests($quotation)['cnb'], $childMarkupMultiplier);
          }
        }
      }
    }

    return [
      'single' => $singleRate,
      'double' => $doubleRate,
      'triple' => $tripleRate,
      'quadruple' => $quadrupleRate,
      'cwb' => $cwbRate,
      'cnb' => $cnbRate,
      'total' => $totalRate
    ];
  }

  function getAttractionPNLData($quotation)
  {

    $groupedAttractionData = []; // Will hold the attraction details
    $total = 0;

    // Loop through the quotation days
    foreach ($quotation['days'] as $dayData) {
        // If there are attractions for this day
        if (!empty($dayData['attractions'])) {
            foreach ($dayData['attractions'] as $attraction) {
                if($attraction['attraction_type'] == "attraction") {
                  $attractionObj = Attraction::with(['placeR', 'typeR', 'attractionRates'])
                  ->findOrFail($attraction['attraction-id']);
                } else if($attraction['attraction_type'] == "city_tour") {
                  $attractionObj = CityTour::with(['placeR', 'typeR', 'attractionRates'])
                  ->findOrFail($attraction['attraction-id']);
                } else if($attraction['attraction_type'] == "excursion") {
                  $attractionObj = Excursion::with(['placeR', 'typeR', 'attractionRates'])
                  ->findOrFail($attraction['attraction-id']);
                }
                $children = QuotationService::categorizeGuests($quotation)['cwb'] + QuotationService::categorizeGuests($quotation)['cnb'];
                $groupedAttractionData['data'][] = [
                    'attraction_name' => $attractionObj->name, // Attraction name (you can fetch it by ID)
                    'city' => $attractionObj->placeR->name, // Attraction name (you can fetch it by ID)
                    'attraction_type' => $attraction['attraction_type'], // Attraction name (you can fetch it by ID)
                    'date' => date('M d, Y', strtotime($dayData['date'])),
                    'adult_price' => $attraction['adult_rate'],
                    'adult_count' => $quotation['adults'],
                    'child_price' => $attraction['child_rate'],
                    'child_count' => $children,
                    'total' => ($attraction['adult_rate'] * $quotation['adults']) + ($attraction['child_rate'] * $children),
                ];

                $total += ($attraction['adult_rate'] * $quotation['adults']) + ($attraction['child_rate'] * $children);
            }
        }
    }
    $groupedAttractionData['total'] = $total;

    return $groupedAttractionData;
  }

}
