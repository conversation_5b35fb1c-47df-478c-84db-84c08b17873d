<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Market extends Model
{
    use HasFactory;

    // Table name
    protected $table = 'market';

    // Primary key
    protected $primaryKey = 'id';

    // Indicates if the model should be timestamped
    public $timestamps = true;

    // Allow mass assignment for the following fields
    protected $fillable = [
        'market',
    ];

    // Many-to-many relationship with User model
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_markets', 'market_id', 'user_id');
    }
}
