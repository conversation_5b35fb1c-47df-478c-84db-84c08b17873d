<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable
{
  use HasRoles;
  use HasApiTokens, HasFactory, Notifiable;

  /**
   * The attributes that are mass assignable.
   *
   * @var array<int, string>
   */
  protected $fillable = ['name', 'status', 'email', 'password'];

  /**
   * The attributes that should be hidden for serialization.
   *
   * @var array<int, string>
   */
  protected $hidden = ['password', 'remember_token'];

  /**
   * The attributes that should be cast.
   *
   * @var array<string, string>
   */
  protected $casts = [
    'email_verified_at' => 'datetime',
    'password' => 'hashed',
  ];

  // One-to-one relationship with Profile model
  public function profile()
  {
    return $this->hasOne(Profile::class);
  }

  public function company()
  {
    return $this->belongsTo(Company::class);
  }

  // Many-to-many relationship with Market model
  public function markets()
  {
    return $this->belongsToMany(Market::class, 'user_markets', 'user_id', 'market_id');
  }
}
