<?php

namespace App\Http\Controllers\Quotation;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Market;
use App\Services\QuotationService;
use App\Services\QuotationManageService;
use App\Services\HotelService;
use App\Services\TransportService;
use App\Services\OtherService;
use App\Services\EmailService;
use App\Models\Place;
use App\Services\AttractionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Quotation;
use App\Models\MemberLanguage;
use App\Models\MemberPosition;
use App\Models\MemberType;
use App\Models\AdminFeeDuration;
use App\Models\AdminFeeHotelCategory;
use App\Models\AdminFeeNoPax;
use App\Models\QuotationHotel;
use App\Models\QuotationSummary;
use App\Models\RunningJob;
use App\Models\QuotationComment;
use App\Models\User;
use Illuminate\Support\Facades\App;
use App\Models\QuotationHotelConfirmation;
use Barryvdh\DomPDF\Facade\Pdf;
use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Support\Facades\View;
use App\Models\QuotationDay;
use App\Models\QuotationDayHotel;

class QuotationController extends Controller
{
  protected $quotationService;
  protected $hotelService;
  protected $attractionService;
  protected $transportService;
  protected $otherService;
  protected $quotationManageService;
  protected $emailService;

  public function __construct(
    QuotationService $quotationService,
    HotelService $hotelService,
    AttractionService $attractionService,
    TransportService $transportService,
    OtherService $otherService,
    QuotationManageService $quotationManageService,
    EmailService $emailService
  ) {
    $this->quotationService = $quotationService;
    $this->hotelService = $hotelService;
    $this->attractionService = $attractionService;
    $this->transportService = $transportService;
    $this->otherService = $otherService;
    $this->quotationManageService = $quotationManageService;
    $this->emailService = $emailService;
  }

  public function start(Request $request)
  {
    $markets = Market::all();
    $quotation = null;
    $referenceId = null;

    // Check if there's a running_job_id in the URL
    if ($request->has('running_job_id') && $request->has('source_type') && $request->source_type == 'running_job') {
      $runningJobId = $request->running_job_id;

      // Store the running_job_id in the session
      session()->put('data.temp_quotation.running_job_id', $runningJobId);
      session()->put('data.temp_quotation.source_type', 'running_job');

      // Retrieve the reference_id from the associated booking
      $runningJob = RunningJob::find($runningJobId);
      if ($runningJob && $runningJob->booking) {
        $referenceId = $runningJob->booking->reference_id;
      }
    }

    if (session()->has('data.quotation')) {
      $quotation = session('data.quotation');
    }

    return view('quotation.start', compact('markets', 'quotation', 'referenceId'));
  }

  public function start_store(Request $request)
  {
    // Validate the input fields
    $request->validate([
      'country' => 'required|string',
      'arrival_date' => 'required|date',
      'departure_date' => 'required|date|after_or_equal:arrival_date',
      'market' => 'required|string',
      'adults' => 'required|integer|min:1',
      'children' => 'nullable|integer',
      'arrival_place' => 'required|string',
      'departure_place' => 'required|string',
      'vehicle' => 'required|integer',
    ]);

    // Determine the season based on arrival date
    $arrivalDate = new \DateTime($request->arrival_date);
    $season = $this->determineSeasonByDate($arrivalDate);

    $newQuotation = [
      'country' => $request->country,
      'reference_no' => $request->reference_no,
      'client_name' => $request->client_name,
      'client_country' => $request->client_country,
      'arrival_date' => $request->arrival_date,
      'departure_date' => $request->departure_date,
      'market' => $request->market,
      'adults' => $request->adults,
      'children' => $request->children,
      'arrival_place' => $request->arrival_place,
      'departure_place' => $request->departure_place,
      'vehicle' => $request->vehicle,
      'children_ages' => $request->children_ages,
      'user' => Auth::user(),
      'currency' => "USD",
      'language' => "en",
      'season_id' => $season['id'] ?? 2,
    ];

    // Preserve running_job_id if it exists in the session
    if (session()->has('data.temp_quotation.running_job_id')) {
      $newQuotation['running_job_id'] = session('data.temp_quotation.running_job_id');
      $newQuotation['source_type'] = session('data.temp_quotation.source_type');
    }

    $vehicle = $this->quotationService->selectVehicle($newQuotation, $request->vehicle);
    $newQuotation['vehicle'] = $vehicle;

    if (session()->has('data.quotation')) {

      $oldQuotation = session('data.quotation');

      $result = QuotationService::checkIfQuotationChanged($oldQuotation, $newQuotation);
      $recreate = false;
      $recreateVehicle = false;

      if ($result['has_changes']) {

        foreach ($result['differences'] as $key => $item) {
          if (isset($newQuotation[$key])) {
            session()->put('data.quotation.' . $key, $newQuotation[$key]);
            if ($key == "vehicle") {
              session()->put('data.quotation.' . $key, $vehicle);
              $recreateVehicle = true;
            }
            if ($key == "arrival_date" || $key == "departure_date" || $key == "adults" || $key == "children" || $key == "market") {
              $recreate = true;
            }
          }
        }
      }

      if ($recreate) {
        $dates = $this->quotationService->getDatesBetween(session('data.quotation'));
        $newQuotation = $this->quotationService->recreateQuotationWithNewDates($newQuotation, session('data.quotation'), $dates);
        session()->put('data.quotation', $newQuotation);
      }

      if ($recreateVehicle) {
        $newQuotation = $this->quotationService->recreateQuotationWithVehicle(session('data.quotation'), $vehicle);
        session()->put('data.quotation', $newQuotation);
      }
      if(session('data.quotation.source_type') == "template") {
          session()->put('data.quotation.user', Auth::user());
      }
      if(session('data.quotation.source_type') == "running_job") {
          session()->put('data.quotation.user', Auth::user());
      }
      // Preserve running_job_id if it exists in the session
      if (session()->has('data.temp_quotation.running_job_id')) {
        session()->put('data.quotation.running_job_id', session('data.temp_quotation.running_job_id'));
      }
      session()->put('data.quotation.reference_no', $request->reference_no ?? null);
      session()->put('data.quotation.client_name', $request->client_name ?? null);
      session()->put('data.quotation.client_country', $request->client_country ?? null);
    } else {
      session()->put('data.quotation', $newQuotation);
      $this->quotationService->generateQuotationDataStart($newQuotation);
    }
    $children = QuotationService::categorizeGuests(session('data.quotation'));
    session()->put('data.quotation.cwb', $children['cwb']);
    session()->put('data.quotation.cnb', $children['cnb']);
    session()->put('data.quotation.infants', $children['infants']);
    session()->put('data.quotation.season_id', $season['id'] ?? 2);

    return redirect()->route('quotation.content');
  }

  public function content()
  {
    $quotation = null;
    $hotels = null;
    $rates = null;
    $runningJob = null;

    if (session()->has('data.quotation')) {
      $quotation = session('data.quotation');
    } else {
      session()->flash('session_expired', true);
      return redirect()->route('quotation.start');
    }

    $dates = $this->quotationService->getDatesBetween($quotation);
    $languages = MemberLanguage::all();
    $positions = MemberPosition::all();
    $types = MemberType::all();

    $durations = AdminFeeDuration::all();
    $categories = AdminFeeHotelCategory::all();
    $pax = AdminFeeNoPax::orderBy('weight', 'asc')->get();

    if (isset($quotation['days']) && count($dates) == count($quotation['days'])) {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
    }
    if (isset($quotation['id'])) {
      $runningJob = RunningJob::where('quotation_id', $quotation['id'])->with(['emails', 'quotation'])->first() ?? null;
    }
    $privateLink = false;

    // dd($rates);
    return view('quotation.content', compact('quotation', 'dates', 'hotels', 'rates', 'languages', 'positions', 'types', 'runningJob', 'privateLink', 'durations', 'categories', 'pax'));
  }

  public function session_save(Request $request)
  {
    $request->validate([
      'start' => 'required_if:day,present|string',
      'end' => 'required_if:day,present|string',
      'hotel' => 'required_if:day,present|array', // Ensure 'hotel' is an array
      'hotel.0' => ['required_if:day,present', 'string', 'json'], // Validate each hotel entry as a JSON string
    ]);

    $data = $request->all();

    if (isset($data['day'])) {
      $i = 0;
      $hotels = null;
      while (isset($data['hotel[' . $i . ']'])) {
        $hotels = json_decode($data['hotel[' . $i . ']'], true);
        $i++;
      }
      // $lastHotel = end($hotels);

      $i = 0;
      $extra_places = [];
      while (isset($data['extra_places[' . $i . ']'])) {
        $extra_places[] = json_decode($data['extra_places[' . $i . ']'], true);
        $i++;
      }

      $i = 0;
      $attractions = [];
      while (isset($data['attraction[' . $i . ']'])) {
        $attractions[] = json_decode($data['attraction[' . $i . ']'], true);
        $i++;
      }

      $meals['breakfast'] = $data['meals[breakfast]'];
      $meals['dinner'] = $data['meals[dinner]'];
      $meals['lunch'] = $data['meals[lunch]'];

      $transports['distance'] = ($data['distance'] < 90) ? 90 : $data['distance'];
      $transports['rate'] = $data['rate'];

      $i = 0;
      $members = [];
      while (isset($data['member[' . $i . ']'])) {
        $members[] = json_decode($data['member[' . $i . ']'], true);
        $i++;
      }

      $i = 0;
      $admin_fees = [];
      while (isset($data['admin_fee[' . $i . ']'])) {
        $admin_fees[] = json_decode($data['admin_fee[' . $i . ']'], true);
        $i++;
      }

      $i = 0;
      $others = [];
      while (isset($data['other[' . $i . ']'])) {
        $others[] = json_decode($data['other[' . $i . ']'], true);
        $i++;
      }

      session()->put('data.quotation.days.' . $data['day'], [
        'day' => $data['day'],
        'date' => $data['date'],
        'start' => $data['start'],
        'end' => $data['end'],
        'extra_places' => $extra_places,
        'hotels' => $hotels,
        'attractions' => $attractions,
        'meals' => $meals,
        'transports' => $transports,
        'members' => $members,
        'admin_fees' => $admin_fees,
        'others' => $others,
        'data_type' => 'permanent', // temporary
      ]);

      $block = [
        'day' => $data['day'],
        'date' => $data['date'],
        'start' => $data['start'],
        'end' => $data['end'],
        'hotels' => $hotels,
        'extra_places' => [],
        'attractions' => [],
        'meals' => $meals,
        'members' => $members,
        'admin_fees' => $admin_fees,
        'others' => $others,
      ];

      $this->quotationService->generateQuotationDataContent($block);
    } else if (isset($data["adult_markup"])) {
      session()->put('data.quotation.adult_markup', $data['adult_markup']);
      session()->put('data.quotation.child_markup', $data['child_markup']);
      session()->put('data.quotation.markup_type', $data['markup_type']);
      session()->put('data.quotation.discount_type', $data['discount_type']);
      session()->put('data.quotation.discount', $data['discount']);

      // Add new markup fields
      if (isset($data['single_markup'])) {
        session()->put('data.quotation.single_markup', $data['single_markup']);
      }
      if (isset($data['double_markup'])) {
        session()->put('data.quotation.double_markup', $data['double_markup']);
      }
      if (isset($data['triple_markup'])) {
        session()->put('data.quotation.triple_markup', $data['triple_markup']);
      }
      if (isset($data['quadruple_markup'])) {
        session()->put('data.quotation.quadruple_markup', $data['quadruple_markup']);
      }

      if (isset($data['save_type'])) {
        session()->put('data.quotation.save_type', $data['save_type']);
      }
    }


    return session('data.quotation');
  }

  public function showDay(Request $request)
  {
    $runningJob = null;
    $day = $request->day;
    $quotation = null;
    if (session()->has('data.quotation')) {
      $quotation = session('data.quotation');
    }
    $dates = $this->quotationService->getDatesBetween($quotation);
    if ($day == 'summery') {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
      $email = false;
      $showBreakDown = !hasAnyRole(['Foreign Agent', 'Foreign Agency']);
      if (isset($quotation['id'])) {
        $runningJob = RunningJob::where('quotation_id', $quotation['id'])->with(['emails', 'quotation'])->first() ?? null;
      }
      $privateLink = false;

      return view('quotation.content.summery', compact('quotation', 'dates', 'hotels', 'rates', 'email', 'showBreakDown', 'runningJob', 'privateLink'))->render();
    } else if ($day == 'save') {
      $privateLink = false;
      $showBreakDown = !hasAnyRole(['Foreign Agent', 'Foreign Agency']);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
      return view('quotation.content.save', compact('quotation', 'dates', 'rates', 'showBreakDown', 'privateLink'))->render();
    } else if ($day == 'save-confirm') {
      $rates = $this->quotationService->calculateQuotationRates($quotation);
      return view('quotation.content.save-confirm', compact('quotation', 'dates', 'rates'))->render();
    } else if ($day == 'confirm-confirm') {
      $rates = $this->quotationService->calculateQuotationRates($quotation);
      return view('quotation.content.confirm-confirm', compact('quotation', 'dates', 'rates'))->render();
    } else {
      $date = $dates[$day];
      $languages = MemberLanguage::all();
      $positions = MemberPosition::all();
      $types = MemberType::all();
      $durations = AdminFeeDuration::all();
      $categories = AdminFeeHotelCategory::all();
      $pax = AdminFeeNoPax::orderBy('weight', 'asc')->get();

      return view('quotation.content.day', compact('quotation', 'day', 'date', 'languages', 'positions', 'types', 'durations', 'categories', 'pax'))->render();
    }
  }

  public function save(Request $request)
  {
    if (session()->has('data.quotation')) {
      $quotation = session('data.quotation');
    } else {
      session()->flash('session_expired', true);
      return redirect()->route('quotation.start');
    }

    $savedQuotation = $this->quotationManageService->saveQuotation($quotation);

    $quotation['quotation_no'] = $savedQuotation->id;
    $quotation['id'] = $savedQuotation->id;
    $quotation['created_at'] = $savedQuotation->created_at;
    session()->put('data.quotation.quotation_no', $savedQuotation->id);
    session()->put('data.quotation.id', $savedQuotation->id);
    session()->put('data.quotation.created_at', $savedQuotation->created_at);
    $quotation['token'] = $savedQuotation->token;
    session()->put('data.quotation.token', $savedQuotation->token);

    if ($quotation['save_type'] == 'confirm') {
      self::sendQuotation($quotation);
    }


    return response()->json([
      'redirect' => route('quotation.finish')
    ]);
  }

  public function review(Request $request)
  {
    $id = $request->id ?? null;
    $quotation_no = $request->quotation_no ?? null;
    $quotation = $this->quotationManageService->reviewQuotation($id, $quotation_no);

    session()->put('data.quotation', $quotation);
    session()->put('data.quotation.source_type', 'update');


    return response()->json([
      'redirect' => route('quotation.content')
    ]);
  }

  public function delete(Request $request)
  {
    $quotation_no = $request->id;
    return $this->quotationManageService->deleteQuotation($quotation_no);
  }

  public function useTemplate(Request $request)
  {
    $quotation_no = $request->quotation_no;
    $quotation = $this->quotationManageService->pullTemplate($quotation_no);

    session()->put('data.quotation', $quotation);
    session()->put('data.quotation.source_type', 'template');

    return response()->json([
      'redirect' => route('quotation.start')
    ]);
  }

  public function sendQuotation($quotation)
  {
    $recipient = Auth::user()->email;; // File handler email address

    $quotation = null;
    $hotels = null;
    $rates = null;

    if (session()->has('data.quotation')) {
      $quotation = session('data.quotation');
    } else {
      session()->flash('session_expired', true);
      return redirect()->route('quotation.start');
    }

    $dates = $this->quotationService->getDatesBetween($quotation);

    if (isset($quotation['days']) && count($dates) == count($quotation['days'])) {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
    }

    // Call the EmailService to send the email
    // return $this->emailService->sendQuotationEmail($quotation, $dates, $hotels, $rates, $recipient);

    return response()->json(['message' => 'Quotation saved successfully']);
  }

  /**
   * Add a new day between existing days
   */
  public function addDay(Request $request)
  {
    $request->validate([
      'after_day' => 'required|integer|min:0',
    ]);

    if (!session()->has('data.quotation')) {
      return response()->json(['error' => 'Session expired'], 400);
    }

    $quotation = session('data.quotation');
    $afterDay = $request->after_day;

    // Add one day to departure date to accommodate the new day
    $departureDate = new \DateTime($quotation['departure_date']);
    $departureDate->modify('+1 day');

    // Update session with new departure date
    session()->put('data.quotation.departure_date', $departureDate->format('Y-m-d'));

    // Regenerate dates
    $newQuotation = session('data.quotation');
    $dates = $this->quotationService->getDatesBetween($newQuotation);

    // Shift existing days data to accommodate the new day
    if (isset($quotation['days'])) {
      $newDays = [];
      foreach ($quotation['days'] as $dayIndex => $dayData) {
        if($dayIndex == $afterDay) {
          $newDays[$afterDay] = $dayData;
        } else if ($dayIndex > $afterDay) {
          // Shift day numbers for days after the insertion point
          $dayData['day'] = $dayIndex + 1;
          $dayData['date'] = $dates[$dayIndex + 1]['date'];
          $newDays[$dayIndex + 1] = $dayData;
        } else {
          $newDays[$dayIndex] = $dayData;
        }
      }
      $dayData['day'] = $dayIndex + 1;
      $dayData['date'] = $dates[$dayIndex + 1]['date'];
      $newDays[$dayIndex + 1] = $dayData;
      session()->put('data.quotation.days', $newDays);
    }
    ksort($newDays); // Sorts by keys in ascending order
    return response()->json([
      'success' => true,
      'message' => 'Day added successfully',
      'new_departure_date' => $departureDate->format('Y-m-d'),
      'redirect' => route('quotation.content')
    ]);
  }

  /**
   * Remove an existing day
   */
  public function removeDay(Request $request)
  {
    $request->validate([
      'day' => 'required|integer|min:1',
    ]);

    if (!session()->has('data.quotation')) {
      return response()->json(['error' => 'Session expired'], 400);
    }

    $quotation = session('data.quotation');
    $dayToRemove = $request->day;

    // Don't allow removing if only one day exists
    $dates = $this->quotationService->getDatesBetween($quotation);
    if (count($dates) <= 1) {
      return response()->json(['error' => 'Cannot remove the last remaining day'], 400);
    }

    // Subtract one day from departure date
    $departureDate = new \DateTime($quotation['departure_date']);
    $departureDate->modify('-1 day');

    // Update session with new departure date
    session()->put('data.quotation.departure_date', $departureDate->format('Y-m-d'));

    // Remove the day and shift subsequent days
    if (isset($quotation['days'])) {
      $newDays = [];
      foreach ($quotation['days'] as $dayIndex => $dayData) {
        if ($dayIndex == $dayToRemove) {
          // Skip the day to be removed
          continue;
        } elseif ($dayIndex > $dayToRemove) {
          // Shift day numbers for days after the removed day
          $dayData['day'] = $dayIndex - 1;
          $dayData['date'] = $dates[$dayIndex - 1]['date'];
          $newDays[$dayIndex - 1] = $dayData;
        } else {
          $newDays[$dayIndex] = $dayData;
        }
      }
      session()->put('data.quotation.days', $newDays);
    }

    return response()->json([
      'success' => true,
      'message' => 'Day removed successfully',
      'new_departure_date' => $departureDate->format('Y-m-d'),
      'redirect' => route('quotation.content')
    ]);
  }

  public function finish(Request $request)
  {
    $quotation = null;
    $allRates = [];

    if (session()->has('data.quotation') && session()->has('data.quotation.quotation_no')) {
      $quotation = session('data.quotation');
    } else {
      session()->flash('session_expired', true);
      return redirect()->route('quotation.start');
    }

    $allRate['hotel'] = $this->hotelService->getHotelPNLData($quotation);
    $allRate['attraction'] = $this->attractionService->getAttractionPNLData($quotation);
    $allRate['transport'] = $this->transportService->getTransportPNLData($quotation);
    $allRate['total'] = $allRate['hotel']['total'] + $allRate['attraction']['total'] + $allRate['transport']['total'];

    $hotelDetails = $this->quotationManageService->getHotelReviews($quotation['id'] ?? $quotation['quotation_no']);
    session()->forget('data.quotation');
    session()->forget('data.temp_quotation');

    return view('quotation.finish', compact('quotation', 'allRate', 'hotelDetails'));
  }

  public function sendHotelVoucher(Request $request)
  {
    $hotelId = $request->input('hotel_id');
    $quotation_id = $request->input('quotation_id');
    $quotation_no = $request->input('quotation_no');
    $quotation = null;
    $allRates = [];

    $quotation = $this->quotationManageService->reviewQuotation($quotation_id, $quotation_no);

    $allRate['hotel'] = $this->hotelService->getHotelPNLData($quotation);
    $hotel = $allRate['hotel']['data'][$hotelId];

    $hotelDetails = $this->quotationManageService->getHotelReviews($quotation['id']);
    $remarks = $hotelDetails[$hotelId] ?? "";
    $emailSent = $this->emailService->sendHotelVoucherEmail($quotation, $hotel, $hotelId, $remarks);

    if ($emailSent) {
      return response()->json([
        'success' => true,
        'message' => 'Hotel voucher sent successfully.',
        'email' => $hotel['contacts'] ?? 'N/A' // Return the hotel's contact email
      ]);
    } else {
      return response()->json([
        'success' => false,
        'message' => 'Failed to send the hotel voucher.'
      ]);
    }
  }

  public function customQuotationReview($id, $quotation_no, Request $request)
  {
    $recipient = Auth::user()->email;; // File handler email address

    $quotation = $this->quotationManageService->reviewQuotation($id, $quotation_no);

    $quotation['quotation_no'] = $quotation['id'];

    $dates = $this->quotationService->getDatesBetween($quotation);

    if (isset($quotation['days']) && count($dates) == count($quotation['days'])) {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
    }

    return view('quotation.partials.quotation-view', compact('quotation', 'dates', 'hotels', 'rates'));
  }

  public function customSendHotelVoucher($id, $quotation_no,  Request $request)
  {

    $quotation = $this->quotationManageService->reviewQuotation($id, $quotation_no);

    // $quotation['quotation_no'] = $quotation['id'];
    $allRate['hotel'] = $this->hotelService->getHotelPNLData($quotation);

    $hotelDetails = $this->quotationManageService->getHotelReviews($quotation['id']);

    return view('quotation.partials.hotel-vouchers-view', compact('quotation', 'allRate', 'hotelDetails'));
  }

  public function customPNLReview($id, $quotation_no, Request $request)
  {

    $quotation = $this->quotationManageService->reviewQuotation($id, $quotation_no);

    $quotation['quotation_no'] = $quotation['id'];

    $allRate['hotel'] = $this->hotelService->getHotelPNLData($quotation);
    $allRate['attraction'] = $this->attractionService->getAttractionPNLData($quotation);
    $allRate['transport'] = $this->transportService->getTransportPNLData($quotation);
    $allRate['other'] = $this->otherService->getOtherPNLData($quotation);
    $allRate['total'] = $allRate['hotel']['total'] + $allRate['attraction']['total'] + $allRate['transport']['total'] + $allRate['other']['total'];

    $rates = $this->quotationService->calculateQuotationRates($quotation, true);

    return view('quotation.partials.pnl-view', compact('quotation', 'allRate', 'rates'));
  }

  public function setCurrency(Request $request)
  {
    $currency = $request->input('currency', 'USD');

    // Validate currency option
    if (!in_array($currency, ['USD', 'EURO'])) {
      return response()->json(['success' => false, 'message' => 'Invalid currency selection'], 400);
    }

    // Set the currency in session
    session()->put('data.quotation.currency', $currency);

    return response()->json(['success' => true]);
  }


  public function setLanguage(Request $request)
  {
    $language = $request->input('language', 'en');

    // Validate currency option
    if (!in_array($language, ['en', 'es', 'fr', 'it'])) {
      return response()->json(['success' => false, 'message' => 'Invalid currency selection'], 400);
    }

    // Set the currency in session
    session()->put('data.quotation.language', $language);

    return response()->json(['success' => true]);
  }


  public function showByToken($token)
  {
    $quotation = Quotation::withTrashed()->where('token', $token)->firstOrFail();
    $user = auth()->user();

    // Check if the quotation is disabled
    if ($quotation->disabled) {
      // If user is not logged in, redirect to login page
      if (!auth()->check()) {
        return redirect()->route('login')->with('message', 'This quotation is currently disabled. Please log in to access it.');
      }

      // Check if user has permission to view disabled quotation
      //$canViewDisabled = $user->hasAnyRole(['Super Admin', 'Admin']) ||
                         // $this->canUserAccessQuotation($user, $quotation);

      if (true) {
        return redirect()->route('dashboard')->with('error', 'This quotation is currently disabled. Please contact an administrator.');
      }
    }

    // Check if user has permission to view this quotation
    if (!$user->hasAnyRole(['Super Admin', 'Admin']) &&
        !$this->canUserAccessQuotation($user, $quotation)) {
      return redirect()->route('dashboard')->with('error', 'You do not have permission to view this quotation.');
    }

    $quotation = $this->quotationManageService->reviewQuotation($quotation->id, null);
    App::setLocale($quotation['language'] ?? 'en');

    $dates = $this->quotationService->getDatesBetween($quotation);

    if (isset($quotation['days']) && count($dates) == count($quotation['days'])) {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
    }
    $runningJob = RunningJob::where('quotation_id', $quotation['id'])->with(['emails', 'quotation'])->first() ?? null;
    $privateLink = true;
    $showBreakDown = false;

    return view('front_end.quotation.quotation-view', compact('quotation', 'dates', 'hotels', 'rates', 'runningJob', 'privateLink', 'showBreakDown'));
  }

  public function showByTokenPdf($token)
  {
    $quotation = Quotation::withTrashed()->where('token', $token)->firstOrFail();
    $user = auth()->user();

    // Check if the quotation is disabled
    if ($quotation->disabled) {
      // If user is not logged in, redirect to login page
      if (!auth()->check()) {
        return redirect()->route('login')->with('message', 'This quotation is currently disabled. Please log in to access it.');
      }

      // Check if user has permission to view disabled quotation
      $canViewDisabled = $user->hasAnyRole(['Super Admin', 'Admin']) ||
                         $this->canUserAccessQuotation($user, $quotation);

      if (!$canViewDisabled) {
        return redirect()->route('dashboard')->with('error', 'This quotation is currently disabled. Please contact an administrator.');
      }
    }

    // Check if user has permission to view this quotation
    if (auth()->check() && !$user->hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant']) &&
        !$this->canUserAccessQuotation($user, $quotation)) {
      return redirect()->route('dashboard')->with('error', 'You do not have permission to view this quotation.');
    }

    $quotation = $this->quotationManageService->reviewQuotation($quotation->id, null);
    App::setLocale($quotation['language'] ?? 'en');

    $dates = $this->quotationService->getDatesBetween($quotation);

    if (isset($quotation['days']) && count($dates) == count($quotation['days'])) {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
    }
    $runningJob = RunningJob::where('quotation_id', $quotation['id'])->with(['emails', 'quotation'])->first() ?? null;
    $privateLink = true;
    $showBreakDown = false;

    // Load the external CSS from the public directory
    $coreCss = file_get_contents(public_path('assets/vendor/css/core.css'));
    $themeCss = file_get_contents(public_path('assets/vendor/css/theme-default.css'));
    $quotationCss = file_get_contents(public_path('assets/css/quotation.css'));

    // Extract CSS variables and replace them
    preg_match_all('/--([\w-]+):\s*(.*?);/', $coreCss, $matches);
    $variables = array_combine($matches[1], $matches[2]);

    // Replace CSS variables with actual values
    foreach ($variables as $var => $value) {
      $coreCss = str_replace("var(--$var)", trim($value), $coreCss);
      $themeCss = str_replace("var(--$var)", trim($value), $themeCss);
      $quotationCss = str_replace("var(--$var)", trim($value), $quotationCss);
    }

    // Add comprehensive PDF-specific CSS for exact design replication
    $pdfSpecificCss = "
      @page {
        margin: 10mm;
        size: A4 portrait;
      }

      body {
        font-family: 'DejaVu Sans', Arial, sans-serif !important;
        font-size: 12px !important;
        line-height: 1.5 !important;
        color: #333 !important;
        background: white !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* Force simple block layout for all elements */
      * {
        box-sizing: border-box !important;
      }

      /* Remove unwanted borders from timeline and cards */
      .card {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      .card-body {
        border: none !important;
        padding: 10px 0 !important;
      }

      .card-header {
        border: none !important;
        background: none !important;
        padding: 10px 0 !important;
      }

      .border-top {
        border-top: none !important;
      }

      /* Timeline specific border removal */
      .timeline-item {
        border: none !important;
        border-left: none !important;
        background: none !important;
        margin-bottom: 15px !important;
        padding: 0 !important;
      }

      /* Remove any remaining borders and spacing */
      .border,
      .border-left,
      .border-right,
      .border-bottom,
      .shadow-sm,
      .rounded-top,
      .rounded {
        border: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* Center logo and company info */
      .card-header .p-3 {
        padding: 10px 0 !important;
        border: none !important;
        text-align: center !important;
      }

      .card-header img {
        display: block !important;
        margin: 0 auto 15px auto !important;
        text-align: center !important;
      }

      /* Hide elements not needed in PDF */
      .position-fixed,
      .btn,
      .d-print-none,
      script,
      .edit-duration,
      .navbar,
      .sidebar {
        display: none !important;
      }

      /* Hide specific sections for PDF using simpler selectors */
      #accordionIncExc-7 {
        display: none !important;
      }

      .accordion-item.active {
        display: none !important;
      }

      /* Hide the last card-body which typically contains invoice */
      .card-body.border-top:last-of-type {
        display: none !important;
      }

      /* Container and layout fixes */
      .container-xxl {
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
      }

      .row {
        margin: 0 !important;
        width: 100% !important;
        overflow: hidden !important;
      }

      .col-12,
      .col-md-4,
      .col-md-9,
      .col-md-3,
      .col {
        width: 100% !important;
        padding: 5px !important;
        display: block !important;
        float: none !important;
        margin: 0 !important;
      }

      /* Simplified layout - avoid complex grid systems */
      .col-md-4 {
        width: 100% !important;
        margin-bottom: 15px !important;
        display: block !important;
      }

      /* Card styling - maintain exact appearance */
      .card {
        border: 1px solid #ddd !important;
        margin-bottom: 15px !important;
        page-break-inside: avoid !important;
        background: white !important;
        border-radius: 8px !important;
      }

      .card-body {
        padding: 20px !important;
      }

      .card-header {
        padding: 20px !important;
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
        text-align: center !important;
      }

      /* Image handling - preserve exact sizing */
      img {
        max-width: 100% !important;
        height: auto !important;
        display: block !important;
        page-break-inside: avoid !important;
        border: none !important;
      }

      .img-fluid {
        max-width: 100% !important;
        height: auto !important;
        display: block !important;
      }

      /* Specific image styling for attractions */
      .img-thumbnail {
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        padding: 4px !important;
        max-width: 200px !important;
        height: auto !important;
      }

      .square-cover-img {
        object-fit: cover !important;
        width: 100% !important;
        height: auto !important;
        max-height: 150px !important;
      }

      /* Table styling - optimized for PDF */
      .table {
        width: 100% !important;
        border-collapse: collapse !important;
        page-break-inside: avoid !important;
        margin-bottom: 15px !important;
        table-layout: fixed !important;
      }

      .table-sm {
        font-size: 10px !important;
      }

      .table th,
      .table td {
        border: 1px solid #ddd !important;
        padding: 6px 4px !important;
        page-break-inside: avoid !important;
        vertical-align: top !important;
        word-wrap: break-word !important;
        overflow: hidden !important;
      }

      .table-dark th,
      .table-dark td {
        background-color: #2c3e50 !important;
        color: white !important;
        border-color: #34495e !important;
        font-size: 9px !important;
        padding: 6px 4px !important;
      }

      .bg-white {
        background-color: white !important;
      }

      /* Hotels table specific styling */
      .table thead th {
        background-color: #2c3e50 !important;
        color: white !important;
        font-size: 9px !important;
        padding: 6px 4px !important;
        text-align: center !important;
        font-weight: bold !important;
        border: 1px solid #34495e !important;
      }

      .table tbody td {
        font-size: 9px !important;
        padding: 6px 4px !important;
        text-align: center !important;
        border: 1px solid #ddd !important;
      }

      /* Timeline styling - simplified for PDF with image positioning */
      .timeline {
        list-style: none !important;
        padding: 0 !important;
        margin: 0 !important;
      }

      .timeline-item {
        margin-bottom: 25px !important;
        page-break-inside: avoid !important;
        border: none !important;
        border-left: none !important;
        padding-left: 30px !important;
        position: relative !important;
        display: flex !important;
        align-items: flex-start !important;
      }

      .timeline-point {
        position: absolute !important;
        left: 0 !important;
        top: 5px !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        background-color: #28a745 !important;
        border: none !important;
        flex-shrink: 0 !important;
      }

      .timeline-point-success {
        background-color: #28a745 !important;
        border: none !important;
      }

      .timeline-event {
        display: flex !important;
        border: none !important;
        width: 100% !important;
        align-items: flex-start !important;
        gap: 15px !important;
      }

      .timeline-event-title {
        margin-bottom: 15px !important;
        border: none !important;
      }

      /* Image positioning in timeline */
      .timeline-event img {
        width: 150px !important;
        height: auto !important;
        max-height: 100px !important;
        object-fit: cover !important;
        border-radius: 8px !important;
        flex-shrink: 0 !important;
        margin-right: 15px !important;
      }

      /* Content positioning in timeline */
      .timeline-event .content {
        flex: 1 !important;
        padding-left: 0 !important;
      }

      /* Day content layout */
      .row.mb-3 {
        display: flex !important;
        align-items: flex-start !important;
        gap: 15px !important;
        margin-bottom: 15px !important;
      }

      .col-md-3 {
        width: 150px !important;
        flex-shrink: 0 !important;
      }

      .col-md-9 {
        flex: 1 !important;
        padding-left: 0 !important;
      }

      /* Flex layout fixes for PDF */
      .d-flex {
        display: block !important;
      }

      .flex-column {
        display: block !important;
      }

      .align-items-center {
        text-align: center !important;
      }

      .justify-content-center {
        text-align: center !important;
      }

      .flex-wrap {
        display: block !important;
      }

      .justify-content-between {
        display: block !important;
      }

      /* Bootstrap spacing overrides - remove excessive spacing */
      .mb-1, .mb-2, .mb-3, .mb-4, .mb-5 { margin-bottom: 8px !important; }
      .mt-1, .mt-2, .mt-3, .mt-4, .mt-5 { margin-top: 8px !important; }
      .p-1, .p-2, .p-3, .p-4, .p-5 { padding: 5px !important; }
      .px-3 { padding-left: 5px !important; padding-right: 5px !important; }
      .py-3 { padding-top: 5px !important; padding-bottom: 5px !important; }

      /* Remove default Bootstrap margins and paddings */
      .container, .container-fluid, .container-xxl {
        padding: 0 !important;
        margin: 0 !important;
        max-width: 100% !important;
      }

      .row {
        margin: 0 !important;
        padding: 0 !important;
      }

      .col, .col-12, .col-md-3, .col-md-9 {
        padding: 0 !important;
        margin: 0 !important;
      }

      /* Remove accordion spacing */
      .accordion-item {
        border: none !important;
        margin-bottom: 10px !important;
      }

      .accordion-body {
        padding: 10px 0 !important;
        border: none !important;
      }

      .accordion-button {
        padding: 8px 0 !important;
        border: none !important;
        background: none !important;
        box-shadow: none !important;
      }

      .accordion-header {
        margin-bottom: 5px !important;
      }

      /* Text utilities - exact colors and alignment */
      .text-center {
        text-align: center !important;
      }

      .text-start,
      .text-left {
        text-align: left !important;
      }

      .text-right {
        text-align: right !important;
      }

      .text-blue {
        color: #00a3d7 !important;
      }

      .text-primary {
        color: #00a3d7 !important;
      }

      .text-muted {
        color: #6c757d !important;
      }

      .text-heading {
        color: #333 !important;
      }

      .text-white {
        color: white !important;
      }

      /* Spacing utilities - exact measurements */
      .mb-0 { margin-bottom: 0 !important; }
      .mb-1 { margin-bottom: 5px !important; }
      .mb-2 { margin-bottom: 10px !important; }
      .mb-3 { margin-bottom: 15px !important; }
      .mb-4 { margin-bottom: 20px !important; }
      .mb-50 { margin-bottom: 50px !important; }
      .mt-3 { margin-top: 15px !important; }
      .p-3 { padding: 15px !important; }
      .pb-4 { padding-bottom: 20px !important; }
      .pe-4 { padding-right: 20px !important; }
      .px-0 { padding-left: 0 !important; padding-right: 0 !important; }
      .px-md-4 { padding-left: 20px !important; padding-right: 20px !important; }

      /* Width utilities */
      .w-50 { width: 50% !important; }
      .w-100 { width: 100% !important; }

      /* Border utilities */
      .border {
        border: 1px solid #ddd !important;
      }

      .border-top {
        border-top: 1px solid #ddd !important;
      }

      .rounded,
      .rounded-top {
        border-radius: 8px !important;
      }

      .shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
      }

      /* Font weights and sizes */
      .fw-medium {
        font-weight: 500 !important;
      }

      .small {
        font-size: 11px !important;
      }

      .h5 {
        font-size: 16px !important;
        font-weight: bold !important;
        margin: 10px 0 !important;
      }

      .h6 {
        font-size: 14px !important;
        font-weight: bold !important;
        margin: 8px 0 !important;
      }

      /* Display utilities */
      .d-block {
        display: block !important;
      }

      /* Position utilities */
      .position-relative {
        position: relative !important;
      }

      /* Gap utility fix */
      .gap-2 {
        margin-bottom: 10px !important;
      }

      /* Page breaks */
      .page-break {
        page-break-before: always !important;
      }

      /* Simplified attraction layout */
      .row.mb-50 {
        margin-bottom: 15px !important;
      }

      .row.mb-50 .col-md-3.col-12 {
        width: 100% !important;
        margin-bottom: 10px !important;
        display: block !important;
      }

      .row.mb-50 .col-md-9.col-12 {
        width: 100% !important;
        display: block !important;
      }

      /* Image thumbnail styling */
      .img-thumbnail {
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        padding: 4px !important;
      }

      .square-cover-img {
        object-fit: cover !important;
        width: 100% !important;
        height: auto !important;
      }

      /* Table responsive fixes */
      .table-responsive {
        overflow: visible !important;
        width: 100% !important;
        display: block !important;
      }

      .horizontal-container {
        overflow: visible !important;
        width: 100% !important;
      }

      /* Remove any container constraints */
      .text-nowrap {
        white-space: normal !important;
      }

      /* Ensure full width for hotel tables */
      .table.mb-3.bg-white.table-sm {
        width: 100% !important;
        table-layout: fixed !important;
      }

      /* Specific column widths for hotels table */
      .table thead th:nth-child(1) { width: 15% !important; } /* Hotel */
      .table thead th:nth-child(2) { width: 12% !important; } /* City */
      .table thead th:nth-child(3) { width: 18% !important; } /* Check-in/out */
      .table thead th:nth-child(4) { width: 8% !important; }  /* Nights */
      .table thead th:nth-child(5) { width: 15% !important; } /* Room Category */
      .table thead th:nth-child(6) { width: 15% !important; } /* Hotel Category */
      .table thead th:nth-child(7) { width: 17% !important; } /* Meal Type */

      /* Accordion and other interactive elements */
      .accordion,
      .accordion-item,
      .accordion-header,
      .accordion-button,
      .accordion-collapse,
      .accordion-body {
        display: block !important;
        border: none !important;
        background: transparent !important;
      }

      .accordion-button {
        font-weight: bold !important;
        padding: 10px 0 !important;
        margin-bottom: 10px !important;
      }

      .accordion-body {
        padding: 0 !important;
      }
    ";

    // Combine all CSS
    $combinedCss = $coreCss . "\n" . $themeCss . "\n" . $quotationCss . "\n" . $pdfSpecificCss;

    // Load view into PDF with PDF mode flag
    $isPdfMode = true;
    $htmlContent = view('front_end.quotation.quotation-view', compact('quotation', 'dates', 'hotels', 'rates', 'runningJob', 'privateLink', 'showBreakDown', 'isPdfMode'))->render();

    // Remove unwanted sections from HTML content
    // Remove dashboard button section
    $htmlContent = preg_replace('/<div class="position-fixed".*?<\/div>/s', '', $htmlContent);

    // Remove @auth section with dashboard button
    $htmlContent = preg_replace('/@auth.*?@endauth/s', '', $htmlContent);

    // Remove conversation history section
    $htmlContent = preg_replace('/<div class="mb-4 card accordion-item active">.*?<\/div>\s*<\/div>\s*<\/div>/s', '', $htmlContent);

    // Remove invoice section (the include statement)
    $htmlContent = preg_replace('/\@include\(\'quotation\.partials\.invoice\'.*?\]/s', '', $htmlContent);

    // Remove any remaining accordion sections with conversation history
    $htmlContent = preg_replace('/<h2 class="accordion-header">.*?conversation_history.*?<\/div>\s*<\/div>\s*<\/div>/s', '', $htmlContent);

    // Convert relative image paths to absolute paths for better PDF compatibility
    $htmlContent = preg_replace_callback('/src=["\']([^"\']+)["\']/', function($matches) {
      $src = $matches[1];
      $originalSrc = $src;

      // Debug: Log the original src for troubleshooting
      \Log::info('PDF Image Processing: Original src = ' . $originalSrc);

      // Handle different image path formats
      if (!filter_var($src, FILTER_VALIDATE_URL)) {
        // Handle asset() URLs and relative paths
        if (strpos($src, 'storage/') === 0) {
          $src = public_path() . '/' . $src;
        } elseif (strpos($src, '/storage/') === 0) {
          $src = public_path() . $src;
        } elseif (strpos($src, 'assets/') === 0) {
          $src = public_path() . '/' . $src;
        } elseif (strpos($src, '/assets/') === 0) {
          $src = public_path() . $src;
        } elseif (strpos($src, '/') === 0) {
          $src = public_path() . $src;
        } else {
          $src = public_path() . '/' . $src;
        }
      } else {
        // Handle absolute URLs - convert to local path
        $parsedUrl = parse_url($src);
        if (isset($parsedUrl['path'])) {
          $localPath = $parsedUrl['path'];
          if (strpos($localPath, '/assets/') === 0) {
            $src = public_path() . $localPath;
          } elseif (strpos($localPath, '/storage/') === 0) {
            $src = public_path() . $localPath;
          }
        }
      }

      // Convert to data URL for better PDF compatibility
      if (file_exists($src)) {
        Log::info('PDF Image Processing: File found at = ' . $src);
        $imageData = base64_encode(file_get_contents($src));
        $imageType = pathinfo($src, PATHINFO_EXTENSION);
        if (in_array(strtolower($imageType), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
          if (strtolower($imageType) === 'jpg') {
            $imageType = 'jpeg';
          }
          $src = "data:image/{$imageType};base64,{$imageData}";
          Log::info('PDF Image Processing: Successfully converted to base64');
        }
      } else {
        Log::warning('PDF Image Processing: File not found at = ' . $src);
        // If file doesn't exist, try alternative paths
        $alternativePaths = [
          public_path('assets/img/branding/' . basename($originalSrc)),
          public_path('assets/img/branding/es_logo.png'), // Fallback to es_logo
          public_path('assets/img/branding/en_logo.png'), // Fallback to en_logo
          storage_path('app/public/' . str_replace('storage/', '', $originalSrc)),
          public_path('storage/' . str_replace('storage/', '', $originalSrc)),
          storage_path('app/public/' . ltrim($originalSrc, '/')),
        ];

        foreach ($alternativePaths as $altPath) {
          Log::info('PDF Image Processing: Trying alternative path = ' . $altPath);
          if (file_exists($altPath)) {
            Log::info('PDF Image Processing: Alternative file found at = ' . $altPath);
            $imageData = base64_encode(file_get_contents($altPath));
            $imageType = pathinfo($altPath, PATHINFO_EXTENSION);
            if (in_array(strtolower($imageType), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
              if (strtolower($imageType) === 'jpg') {
                $imageType = 'jpeg';
              }
              $src = "data:image/{$imageType};base64,{$imageData}";
              Log::info('PDF Image Processing: Successfully converted alternative image to base64');
              break;
            }
          }
        }
      }

      // Final fallback - if still no image found, use a default logo
      if (strpos($src, 'data:image') === false && strpos($originalSrc, 'logo') !== false) {
        $defaultLogo = public_path('assets/img/branding/es_logo.png');
        if (file_exists($defaultLogo)) {
          $imageData = base64_encode(file_get_contents($defaultLogo));
          $src = "data:image/png;base64,{$imageData}";
          Log::info('PDF Image Processing: Used default logo fallback');
        }
      }

      return 'src="' . $src . '"';
    }, $htmlContent);

    // Apply inline styles for better PDF rendering
    $inliner = new CssToInlineStyles();
    $htmlWithInlineStyles = $inliner->convert($htmlContent, $combinedCss);

    // Debug option - uncomment to see HTML output instead of PDF
    // return response($htmlWithInlineStyles)->header('Content-Type', 'text/html');

    try {
      // Create PDF with optimized settings
      $pdf = Pdf::loadHTML($htmlWithInlineStyles)
        ->setPaper('A4', 'portrait')
        ->setOption('margin-top', 10)
        ->setOption('margin-right', 10)
        ->setOption('margin-bottom', 10)
        ->setOption('margin-left', 10)
        ->setOption('enable-local-file-access', true)
        ->setOption('images', true)
        ->setOption('print-media-type', true)
        ->setOption('dpi', 96)
        ->setOption('defaultFont', 'DejaVu Sans');

      // Return PDF for download
      return $pdf->download('quotation-' . $quotation["reference_no"] . '.pdf');
    } catch (\Exception $e) {
      // If PDF generation fails, return error with HTML for debugging
      return response()->json([
        'error' => 'PDF generation failed: ' . $e->getMessage(),
        'html_preview' => $htmlWithInlineStyles
      ], 500);
    }

    // return view('front_end.quotation.quotation-view', compact('quotation', 'dates', 'hotels', 'rates', 'runningJob', 'privateLink', 'showBreakDown'));
  }

  public function previewByToken($token)
  {
    $quotation = Quotation::withTrashed()->where('token', $token)->firstOrFail();
    $user = auth()->user();

    // Check if the quotation is disabled
    if ($quotation->disabled) {
      // If user is not logged in, redirect to login page
      if (!auth()->check()) {
        return redirect()->route('login')->with('message', 'This quotation is currently disabled. Please log in to access it.');
      }

      // Check if user has permission to view disabled quotation
      $canViewDisabled = $user->hasAnyRole(['Super Admin', 'Admin']) ||
                         $this->canUserAccessQuotation($user, $quotation);

      if (!$canViewDisabled) {
        return redirect()->route('dashboard')->with('error', 'This quotation is currently disabled. Please contact an administrator.');
      }
    }

    // Check if user has permission to view this quotation
    if (!$user->hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant']) &&
        !$this->canUserAccessQuotation($user, $quotation)) {
      return redirect()->route('dashboard')->with('error', 'You do not have permission to view this quotation.');
    }

    $quotation = $this->quotationManageService->reviewQuotation($quotation->id, null);
    App::setLocale($quotation['language'] ?? 'en');

    $dates = $this->quotationService->getDatesBetween($quotation);

    if (isset($quotation['days']) && count($dates) == count($quotation['days'])) {
      $hotels = $this->hotelService->getHotelSummeryData($quotation);
      $rates = $this->quotationService->calculateQuotationRates($quotation);
    }
    $runningJob = RunningJob::where('quotation_id', $quotation['id'])->with(['emails', 'quotation'])->first() ?? null;
    $privateLink = true;
    $showBreakDown = !hasAnyRole(['Foreign Agent', 'Foreign Agency']);

    return view('front_end.quotation.quotation-view', compact('quotation', 'dates', 'hotels', 'rates', 'runningJob', 'privateLink', 'showBreakDown'));
  }

  public function saveCommentToDatabase(Request $request)
  {
    $request->validate([
        'quotation_id' => 'required|integer',
        'comment_id' => 'required|integer',
        'comment' => 'required|string',
    ]);

    // Get the quotation
    $quotation = $this->quotationManageService->reviewQuotation(null , $request->quotation_id);

    // Save comment in the database
    $comment = QuotationComment::create([
        'quotation_id' => $request->quotation_id,
        'user_id' => Auth::user()->id,
        'comment_id' => $request->comment_id,
        'comment' => $request->comment,
    ]);

    // Get all recipients for the notification
    $recipients = collect();

    // Get running job if available
    $runningJob = RunningJob::where('quotation_id', $request->quotation_id)->first();

    // Add quotation assignees if running job exists
    if ($runningJob && $runningJob->quotationAssignees && $runningJob->quotationAssignees->count() > 0) {
        $recipients = $recipients->merge($runningJob->quotationAssignees);
    }

    // Add booking assignee if exists
    if ($runningJob && $runningJob->booking && $runningJob->booking->assignee) {
        $recipients->push($runningJob->booking->assignee);
    }

    // Add customer email if exists (client_name stores customer user ID)
    if ($quotation['client_name']) {
        $customer = User::find($quotation['client_name']);
        if ($customer && $customer->email) {
            $recipients->push($customer);
        }
    }

    // Remove duplicates and the current user
    $recipients = $recipients->unique('id')->filter(function ($user) {
        return $user->id !== Auth::user()->id;
    });

    // Send email to all recipients
    if ($recipients->count() > 0) {
        $senderName = Auth::user()->name;

        // Load user relationship for the comment
        $comment->load('user');

        foreach ($recipients as $recipient) {
            if ($recipient->email) {
                \Mail::to($recipient->email)->queue(
                    new \App\Mail\ItineraryCommentNotification($comment, $quotation, $senderName)
                );
            }
        }

        // Also send WhatsApp notifications
        \App\Jobs\SendWhatsAppNotification::dispatch($recipients, $comment, $quotation, $senderName);
    }

    // Prepare response data
    $responseComment = [
        "user_name" => Auth::user()->name,
        "comment" => $comment->comment,
        "created_at" => $comment->created_at->format('Y-m-d H:i:s'),
    ];

    return response()->json(['comment' => $responseComment, 'success' => true, 'message' => 'Comment saved successfully.']);
  }

  public function saveRemarks(Request $request)
  {
    $request->validate([
      'quotation_id' => 'required|exists:quotations,id',
      'hotel_id' => 'required|exists:hotels,id',
      'remarks' => 'nullable|string'
    ]);

    // Check if a record exists
    $hotel = QuotationHotel::firstOrNew([
      'quotation_id' => $request->quotation_id,
      'hotel_id' => $request->hotel_id
    ]);

    // Update or set the remarks
    $hotel->remarks = $request->remarks;
    $hotel->save();

    return response()->json(['success' => true, 'message' => 'Remarks saved successfully.']);
  }

  public function updateHotelRate(Request $request)
  {
      // Validate the input
      $request->validate([
          'quotation_id' => 'required|integer',
          'day' => 'required|integer',
          'hotel_id' => 'required|integer',
          'discount_type' => 'required|string',
          'discount_rate' => 'required|numeric',
      ]);

      // Map room_type to the actual field name in the DB
      $rateFields = [
          'SNL'     => 'discounted_adult_rate_single',
          'DBL'     => 'discounted_adult_rate_double',
          'TPL'     => 'discounted_adult_rate_triple',
          'QUA'     => 'discounted_adult_rate_quadruple',
          'cwb'        => 'discounted_cwb_rate',
          'cnb'        => 'discounted_cnb_rate',
      ];

      // Check if room_type is valid
      if (!array_key_exists($request->discount_type, $rateFields)) {
          return response()->json(['success' => false, 'message' => 'Invalid room type'], 400);
      }

      // Find all matching QuotationDays
      $quotationDays = QuotationDay::where('quotation_id', $request->quotation_id)->get();

      if ($quotationDays->isEmpty()) {
          return response()->json(['success' => false, 'message' => 'Quotation days not found'], 404);
      }

      $updated = false;

      foreach ($quotationDays as $quotationDay) {
          // Find the associated QuotationDayHotel
          $quotationHotel = QuotationDayHotel::where('quotation_day_id', $quotationDay->id)
              ->where('hotel_id', $request->hotel_id)
              ->first();

          if ($quotationHotel) {
              // Update the correct rate field
              $fieldToUpdate = $rateFields[$request->discount_type] ?? null;

              if ($fieldToUpdate) {
                  $quotationHotel->$fieldToUpdate = $request->discount_rate;
                  $quotationHotel->save();
                  $updated = true;
              }
          }
      }

      if (!$updated) {
          return response()->json(['success' => false, 'message' => 'No hotels updated'], 404);
      }

      $id = $request->quotation_id;
      $quotation = $this->quotationManageService->reviewQuotation($id, null);

      // $quotation['quotation_no'] = $quotation['id'];
      $allRate['hotel'] = $this->hotelService->getHotelPNLData($quotation);
      $hotel = $allRate['hotel']['data'][$request->hotel_id];
      $hotel_id = $request->hotel_id;
      $remarks = $hotelDetails[$request->hotel_id] ?? null;

      $hotelDetails = $this->quotationManageService->getHotelReviews($quotation['id']);

      return view('quotation.partials.hotel-voucher-invoice', compact('quotation', 'hotel', 'hotel_id', 'remarks'))->render();

      // return response()->json(['success' => true, 'message' => 'Rate updated successfully']);
  }

  /**
   * Update hotel discount title
   */
  public function updateDiscountTitle(Request $request)
  {
    $request->validate([
        'quotation_id' => 'required',
        'hotel_id' => 'required',
        'discount_title' => 'required|string|max:255'
    ]);

    // Find all matching QuotationDays
    $quotationDays = QuotationDay::where('quotation_id', $request->quotation_id)->get();

    if ($quotationDays->isEmpty()) {
        return response()->json(['success' => false, 'message' => 'Quotation days not found'], 404);
    }

    $updated = false;

    foreach ($quotationDays as $quotationDay) {
        // Find the associated QuotationDayHotel
        $quotationHotel = QuotationDayHotel::where('quotation_day_id', $quotationDay->id)
            ->where('hotel_id', $request->hotel_id)
            ->first();

        if ($quotationHotel) {
            $quotationHotel->discount_title = $request->discount_title;
            $quotationHotel->save();
            $updated = true;
        }
    }

    if (!$updated) {
        return response()->json(['success' => false, 'message' => 'No hotels updated'], 404);
    }

    return response()->json(['success' => true]);
  }

  public function confirmationReport($quotation_id, $quotation_no, Request $request)
  {
    $quotation = $this->quotationManageService->reviewQuotation($quotation_id, $quotation_no);

    $hotels = $this->hotelService->getHotelSummeryData($quotation);
    $hotelStatus = $this->hotelService->getHotelStatusData($quotation_no);

    return view('quotation.partials.confirmation-report', compact('quotation', 'hotels', 'hotelStatus'));
  }

  public function confirmationReportPdf($quotation_id, $quotation_no, Request $request)
  {

    $quotation = $this->quotationManageService->reviewQuotation($quotation_id, $quotation_no);

    $hotels = $this->hotelService->getHotelSummeryData($quotation);
    $hotelStatus = $this->hotelService->getHotelStatusData($quotation_no);

    // Load the external CSS from the public directory
    $coreCss = file_get_contents(public_path('assets/vendor/css/core.css'));
    $themeCss = file_get_contents(public_path('assets/vendor/css/theme-default.css'));
    $quotationCss = file_get_contents(public_path('assets/css/quotation.css')); // Load the local CSS file

    // Extract CSS variables
    preg_match_all('/--([\w-]+):\s*(.*?);/', $coreCss, $matches);

    $variables = array_combine($matches[1], $matches[2]); // Convert to key-value array

    // Replace `var(--white)` with `#ffffff`, etc.
    foreach ($variables as $var => $value) {
      $coreCss = str_replace("var(--$var)", trim($value), $coreCss);
    }

    // Combine all CSS into one string
    $combinedCss = $coreCss . "\n" . $themeCss . "\n" . $quotationCss;

    // Load view into PDF
    $htmlContent = view('front_end.quotation.confirmation-report', compact('quotation', 'hotels', 'hotelStatus'))->render();

    $inliner = new CssToInlineStyles();
    $htmlWithInlineStyles = $inliner->convert($htmlContent, $combinedCss);

    $pdf = Pdf::loadHTML($htmlWithInlineStyles)
      ->setPaper('a4') // Set landscape for full width
      ->setOption('margin-left', 0)
      ->setOption('margin-right', 0)
      ->setOption('margin-top', 0)
      ->setOption('margin-bottom', 0);

    // Return PDF for download
    return $pdf->download('hotel-confirmation-report- ' . $quotation["quotation_no"] . '.pdf');
    return response($htmlWithInlineStyles)->header('Content-Type', 'text/html');

    // return view('front_end.quotation.confirmation-report', compact('quotation', 'hotels', 'hotelStatus'));
  }

  public function updateStatus(Request $request)
  {
    $request->validate([
      'quotation_id' => 'required|exists:quotations,id',
      'hotel_id' => 'required|exists:hotels,id',
      'status' => 'required|string|in:Pending,Confirmed'
    ]);

    // Find or create a record
    $confirmation = QuotationHotelConfirmation::updateOrCreate(
      [
        'quotation_id' => $request->quotation_id,
        'hotel_id' => $request->hotel_id
      ],
      [
        'status' => $request->status
      ]
    );

    return response()->json([
      'success' => true,
      'message' => 'Hotel confirmation status updated successfully.',
      'data' => $confirmation
    ]);
  }

  public function saveSummeryUl(Request $request)
  {
    $request->validate([
      'ul_id' => 'required',
      'updated_list' => 'nullable|string'
    ]);

    session()->put('data.quotation.summery.policy.' . $request->ul_id, $request->updated_list);

    return response()->json(['success' => true, 'message' => 'Summary updated successfully.']);
  }

  public function saveSupplements(Request $request)
  {
      $request->validate([
          'updated_list' => 'nullable|string',
      ]);

      session()->put('data.quotation.summery.supplements', $request->updated_list);

      return response()->json(['success' => true, 'message' => 'Summary updated successfully.']);
  }


  public function saveDuration(Request $request)
  {
      $request->validate([
          'day_id' => 'required',
          'duration' => 'required|string'
      ]);

      // Save to session
      session()->put('data.quotation.summery.duration.' . $request->day_id, $request->duration);

      return response()->json(['success' => true, 'message' => 'Duration updated successfully.']);
  }


  public function saveComment(Request $request)
  {
    $request->validate([
      'comment_id' => 'required',
      'comment' => 'nullable|string'
    ]);

    session()->put(
      'data.quotation.summery.comments.' . (int)$request->comment_id,
      [
        "text" => $request->comment,
        "user" => Auth::user(),
        "created_at" => Now(),
      ]
    );
    $comment = [
      "user_name" => Auth::user()->name,
      "comment" => $request->comment,
      "created_at" => Now(),

    ];
    return response()->json(['comment' => $comment, 'success' => true, 'message' => 'Comment saved successfully.']);
  }

  public function showQuotationSession()
  {
    $quotation = session('data.quotation'); // Retrieve the quotation session
    $jsonQuotation = json_encode($quotation, JSON_PRETTY_PRINT); // Convert to a JSON object

    return view('quotation.develop.show', compact('jsonQuotation')); // Return to the view
  }

  // Save quotation session from JSON object
  public function saveQuotationSession(Request $request)
  {
    $request->validate([
      'quotation_json' => 'required|json'
    ]);

    $quotationData = json_decode($request->input('quotation_json'), true);
    session(['data.quotation' => $quotationData]);

    return response()->json(['success' => true, 'message' => 'Quotation session saved successfully!']);
  }

  // Clear the quotation session
  public function clearQuotationSession()
  {
    session()->forget('data.quotation');
    return response()->json(['success' => true, 'message' => 'Quotation session cleared successfully!']);
  }

  public function show_session(Request $request)
  {
    dd(session('data'));
  }

  /**
   * Determine the season based on a given date
   *
   * @param \DateTime $date
   * @return array|null
   */
  private function determineSeasonByDate(\DateTime $date)
  {
    $seasons = config('custom.seasons');
    $monthDay = $date->format('m-d');
    $year = $date->format('Y');

    foreach ($seasons as $season) {
      foreach ($season['date_ranges'] as $range) {
        // Handle ranges that span across years (e.g., Dec to Jan)
        if ($this->isDateInMonthDayRange($monthDay, $range['start'], $range['end'], $year)) {
          return $season;
        }
      }
    }

    return null; // No matching season found
  }

  /**
   * Check if a date falls within a month-day range
   *
   * @param string $monthDay Format: 'mm-dd'
   * @param string $rangeStart Format: 'mm-dd'
   * @param string $rangeEnd Format: 'mm-dd'
   * @param string $year Current year
   * @return bool
   */
  private function isDateInMonthDayRange($monthDay, $rangeStart, $rangeEnd, $year)
  {
    // Handle ranges that cross year boundary (e.g., Dec 15 to Jan 15)
    if ($rangeStart > $rangeEnd) {
      // If date is between start of range and end of year OR between start of year and end of range
      return ($monthDay >= $rangeStart && $monthDay <= '12-31') ||
             ($monthDay >= '01-01' && $monthDay <= $rangeEnd);
    } else {
      // Normal range within same year
      return $monthDay >= $rangeStart && $monthDay <= $rangeEnd;
    }
  }

  /**
   * Check if a user can access a quotation based on their relationship to it
   *
   * @param User $user
   * @param Quotation $quotation
   * @return bool
   */
  private function canUserAccessQuotation($user, $quotation)
  {
    // Customer assigned to this quotation
    if ($user->hasRole('Customer') && $quotation->client_name == $user->id) {
      return true;
    }

    // Get the running job associated with this quotation
    $runningJob = RunningJob::where('quotation_id', $quotation->quotation_no)->first();

    // Check if user is a booking assignee
    if ($runningJob && $runningJob->booking && $runningJob->booking->assignee_id == $user->id) {
      return true;
    }

    // Check if user is a quotation assignee
    if ($runningJob) {
      $isAssignee = $runningJob->quotationAssignees()
        ->where('user_id', $user->id)
        ->exists();

      if ($isAssignee) {
        return true;
      }
    }

    return false;
  }
}
