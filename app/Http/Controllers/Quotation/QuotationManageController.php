<?php

namespace App\Http\Controllers\Quotation;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Market;
use App\Services\QuotationService;
use App\Services\QuotationManageService;
use App\Services\HotelService;
use App\Services\TransportService;
use App\Services\EmailService;
use App\Models\Place;
use App\Services\AttractionService;
use Illuminate\Support\Facades\Auth;
use App\Models\Quotation;

class QuotationManageController extends Controller
{
  protected $quotationService;
  protected $hotelService;
  protected $attractionService;
  protected $transportService;
  protected $quotationManageService;
  protected $emailService;

  public function __construct(
    QuotationService $quotationService,
    HotelService $hotelService,
    AttractionService $attractionService,
    TransportService $transportService,
    QuotationManageService $quotationManageService,
    EmailService $emailService
  ) {
    $this->quotationService = $quotationService;
    $this->hotelService = $hotelService;
    $this->attractionService = $attractionService;
    $this->transportService = $transportService;
    $this->quotationManageService = $quotationManageService;
    $this->emailService = $emailService;
  }

  public function index()
  {
    $attractionTypes = \App\Models\AttractionType::orderBy('type')->get();
    $hotelClasses = \App\Models\HotelClass::orderBy('class')->get();


    return view('quotation.index', compact('attractionTypes', 'hotelClasses'));
  }

  public function getList(Request $request)
  {
    if ($request->ajax()) {
      // Capture the relevant parameters from the DataTables request
      $searchValue = $request->input('search.value'); // Search value
      $orderColumnIndex = $request->input('order.0.column', null); // Column index for sorting
      $orderDirection = $request->input('order.0.dir', 'desc'); // Sort direction (asc/desc), default to 'desc'
      $columns = $request->input('columns'); // Columns from DataTables

      // Mapping column index to the corresponding table column names
      $orderColumn = $orderColumnIndex !== null ? $columns[$orderColumnIndex]['data'] : 'id'; // Default to 'id' if no column is provided

      // Ensure sorting by ID in descending order if sorting is not set
      if ($orderColumnIndex === null) {
          $orderColumn = 'id';
          $orderDirection = 'desc';
      }

      // Get current user and their roles (using Spatie Permission package)
      $user = Auth::user();
      $isSuperAdmin = $user->hasRole('Super Admin');
      $isAdmin = $user->hasRole('Admin');

      // Fetch the quotations for the authenticated user, applying filters and sorting
      $quotations = Quotation::with(['days.hotels', 'days.attractions']) // Eager load
        ->when($request->input('type') != "inquiry" && !$isSuperAdmin && !$isAdmin, function ($query) {
            // For non-admin users, show only their own quotations
            return $query->where('user_id', Auth::id());
        })
        ->when($request->input('type') != "inquiry" && $isAdmin && !$isSuperAdmin, function ($query) use ($user) {
            // For Admin users (not Super Admin), filter by their assigned markets
            $userMarkets = $user->markets->pluck('id')->toArray();

            if (!empty($userMarkets)) {
                return $query->whereIn('market', $userMarkets);
            }

            // If admin has no markets assigned, show no quotations
            return $query->where('id', 0);
        })
        ->when($request->input('type') == "inquiry" && $request->input('quotation_no'), function ($query) use ($request) {
            return $query->where('quotation_no', $request->input('quotation_no'));
        })
        ->when($request->input('tour_days'), function ($query, $value) {
            if ($value == '10') {
                $query->whereHas('days', function ($q) {
                    $q->groupBy('quotation_id')->havingRaw('COUNT(*) >= 10');
                });
            } else {
                $query->whereHas('days', function ($q) use ($value) {
                    $q->groupBy('quotation_id')->havingRaw('COUNT(*) = ?', [$value]);
                });
            }
        })
        ->when($request->input('pax'), function ($query, $value) {
            $query->whereRaw('(adults + children) = ?', [$value]);
        })
        ->when($request->input('attraction_type'), function ($query, $value) {
            $query->whereHas('days.attractions.attraction', function ($q) use ($value) {
                $q->where('type', $value);
            });
        })
        ->when($request->input('arrival_month'), function ($query, $value) {
            $query->whereMonth('arrival_date', '=', date('m', strtotime($value)))
                  ->whereYear('arrival_date', '=', date('Y', strtotime($value)));
        })
        ->when($request->input('attraction_name'), function ($query, $value) {
            $query->whereHas('days.attractions.attraction', function ($q) use ($value) {
                $q->where('name', 'like', "%{$value}%");
            });
        })
        ->when($request->input('hotel_class'), function ($query, $value) {
            $query->whereHas('days.hotels.hotel', function ($q) use ($value) {
                $q->where('class', $value); // assuming `class` field exists in Hotel model
            });
        })
        ->when($searchValue, function ($query) use ($searchValue) {
            $query->where(function ($sub) use ($searchValue) {
                $sub->where('id', 'like', "%{$searchValue}%")
                    ->orWhere('reference_no', 'like', "%{$searchValue}%")
                    ->orWhere('save_type', 'like', "%{$searchValue}%")
                    ->orWhere('total', 'like', "%{$searchValue}%");
            });
        })
        ->when($request->input('quotation_no'), function ($query, $value) {
            return $query->where('quotation_no', $value);
        })
        ->orderBy($orderColumn, $orderDirection)
        ->paginate($request->input('length'), ['*'], 'page', $request->input('start') / $request->input('length') + 1);

      // Map the quotation data for DataTables
      $quotationsData = $quotations->map(function ($quotation) {
        return [
          'id' => $quotation->id,
          'quotation_no' => $quotation->quotation_no,
          'reference_no' => $quotation->reference_no,
          'created_at' => $quotation->created_at->format('Y-m-d'),
          'save_type' => $quotation->save_type == 'save' ? "Saved" : "Confirmed",
          'user' => \App\Models\User::find($quotation->user_id)->name??"",
          'token' => $quotation->token,
          'total' => '$' . number_format($quotation->total, 2),
          'disabled' => $quotation->disabled,
          'current_version_id' => $quotation->id, // Used for the toggle
          'versions' => $quotation->versions->map(function ($version) {
              return [
                  'id' => $version->id,
                  'quotation_no' => $version->quotation_no,
                  'token' => $version->token,
                  'disabled' => $version->disabled,
                  'reference_no' => $version->reference_no,
                  'save_type' => $version->save_type == 'save' ? "Saved" : "Confirmed",
                  'user' => \App\Models\User::find($version->user_id)->name ?? "",
                  'total' => '$' . number_format($version->total, 2),
              ];
          }),
        ];
      });

      // Return the JSON response in the format DataTables expects
      return response()->json([
        'draw' => intval($request->input('draw')), // Draw counter from DataTables
        'recordsTotal' => $quotations->total(), // Total number of records (without filtering)
        'recordsFiltered' => $quotations->total(), // Total number of records after filtering
        'data' => $quotationsData, // Data for the current page
      ]);
    }
  }

  public function search(Request $request)
  {
    $search = $request->input('search');

    $quotations = Quotation::query()
      ->when($search, function ($query) use ($search) {
        $query->where('id', 'like', "%{$search}%")
          ->orWhere('arrival_date', 'like', "%{$search}%")
          ->orWhere('departure_date', 'like', "%{$search}%");
      })
      ->limit(10) // Limit results for performance
      ->orderBy('id', 'DESC')
      ->get(['id', 'quotation_no', 'reference_no', 'arrival_date', 'departure_date']);

    return response()->json($quotations);
  }

  public function details($id)
  {
    $quotation = Quotation::with('days.members.language', 'days.members.position', 'days.members.type')->findOrFail($id);

    // Get the first day
    $firstDay = $quotation->days->first();

    // Check if a day exists, and process its members
    if ($firstDay) {
      $members = $firstDay->members->map(function ($member) {
        return [
          'language' => $member->language->name ?? null,
          'position' => $member->position->name ?? null,
          'type' => $member->type->name ?? null,
        ];
      });
    } else {
      $members = [];
    }

    return response()->json([
      'start_date' => $quotation->arrival_date,
      'end_date' => $quotation->departure_date,
      'members' => $members,
    ]);
  }

  /**
   * Toggle the disabled status of a quotation.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function toggleStatus(Request $request)
  {
      $request->validate([
          'quotation_id' => 'required|exists:quotations,id',
          'version_id' => 'nullable|exists:quotations,id',
          'disabled' => 'required',
      ]);

      $quotationId = $request->version_id ?? $request->quotation_id;
      $quotation = Quotation::withTrashed()->findOrFail($quotationId);

      $quotation->disabled = filter_var($request->disabled, FILTER_VALIDATE_BOOLEAN);
      $quotation->save();

      return response()->json([
          'success' => true,
          'message' => 'Quotation status updated successfully.',
          'disabled' => filter_var($quotation->disabled, FILTER_VALIDATE_BOOLEAN)
      ]);
  }

  /**
   * Get the disabled status of a quotation version.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function getVersionStatus(Request $request)
  {
      $request->validate([
          'version_id' => 'required|exists:quotations,id',
      ]);

      $quotation = Quotation::withTrashed()->findOrFail($request->version_id);

      return response()->json([
          'success' => true,
          'disabled' => $quotation->disabled
      ]);
  }
}
