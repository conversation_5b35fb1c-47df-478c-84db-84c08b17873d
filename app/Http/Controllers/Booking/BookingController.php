<?php

namespace App\Http\Controllers\Booking;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\User;
use App\Models\RunningJob;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class BookingController extends Controller
{
    // Handle booking form submission
    public function store(Request $request)
    {
        // Validate request data
        $request->validate([
            'full_name' => 'required|string|max:255',
            'pax' => 'required|integer',
            'phone' => 'required|string',
            'email' => 'required|email',
            'message' => 'nullable|string',
            'whatsapp_contact' => 'nullable',
            'market_id' => 'nullable|exists:market,id'
        ]);

        // Format the message field with all details
        $formattedMessage = "Booking Details:\n\n";
        $formattedMessage .= "----------------------------------------\n\n";
        $formattedMessage .= "📌 Tour Itinerary: " . $request->input('name') . "\n";
        $formattedMessage .= "📌 Name: " . $request->input('full_name') . "\n";
        $formattedMessage .= "📧 Email: " . $request->input('email') . "\n";
        $formattedMessage .= "📞 Phone: " . $request->input('phone') . "\n";
        $formattedMessage .= "📅 Booking Date: " . now()->format('Y-m-d') . "\n"; // Current date
        $formattedMessage .= "👥 # of Participants: " . $request->input('pax') . "\n";

        // WhatsApp contact status
        $whatsappContact = $request->has('whatsapp_contact') ? 'Yes' : 'No';
        $formattedMessage .= "📲 Contact via WhatsApp: " . $whatsappContact . "\n";

        // Message field
        $formattedMessage .= "📝 Message: " . ($request->input('message') ?? 'N/A') . "\n";
        $formattedMessage .= "📌 Status: new\n";
        $formattedMessage .= "\n----------------------------------------\n";

        // Save booking
        $booking = Booking::create([
            'template_id' => $request->input('template_id'),
            'market_id' => $request->input('market_id'),
            'message' => $formattedMessage,
            'assignee_id' => null, // Update if applicable
            'reference_id' => $request->input('reference_id'), // Update if applicable
        ]);

        // Create running job
        $runningJob = RunningJob::create([
            'booking_id' => $booking->id,
        ]);

        // Automatically assign the authenticated user as a quotation assignee
        if (auth()->check() && auth()->user()->hasRole('Foreign Agent')) {
            // only if the form was submitted from the office (created_by = office)
            if ($request->input('created_from') === 'office') {
                // For other roles, use the requested assignee_id
                $booking->update([
                    'assignee_id' => Auth::id(),
                ]);
            }
        } else {
            if ($request->input('created_from') === 'office') {
                $runningJob->quotationAssignees()->sync([Auth::id()]);
            }
        }

        return redirect()->back()->with('success', 'Booking successfully created!');
    }

    public function form_1_store(Request $request)
    {
        // Validate request data
        $request->validate([
            'group_composition' => 'required|string|max:255',
            'num_travelers' => 'required|integer|min:1',
            'num_adults' => 'nullable|integer|min:0',
            'young_16' => 'nullable|integer|min:0',
            'kids_11' => 'nullable|integer|min:0',
            'babies_2' => 'nullable|integer|min:0',
            'num_days' => 'required|integer|min:1',
            'departure_date' => 'required|date',
            'interests' => 'nullable|array',
            'accommodation' => 'required|string|max:255',
            'companion' => 'required|string|max:255',
            'agency_name' => 'required|string|max:255',
            'agent_name' => 'required|string|max:255',
            'client_name' => 'nullable|string|max:255',
            'whatsapp' => 'required|string',
            'email' => 'required|email',
            'comments' => 'nullable|string',
        ]);

        // Format the message with all booking details
        $formattedMessage = "📌 **New Booking Request**\n\n";
        $formattedMessage .= "----------------------------------------\n\n";
        $formattedMessage .= "👤 **Group Composition:** " . $request->input('group_composition') . "\n";
        $formattedMessage .= "👥 **Number of Travelers:** " . $request->input('num_travelers') . "\n";
        $formattedMessage .= "📅 **Departure Date:** " . $request->input('departure_date') . "\n";
        $formattedMessage .= "📆 **Trip Duration:** " . $request->input('num_days') . " days\n";
        $formattedMessage .= "🏨 **Accommodation Type:** " . $request->input('accommodation') . "\n";
        $formattedMessage .= "🚗 **Companion Type:** " . $request->input('companion') . "\n\n";

        // Travelers breakdown
        $formattedMessage .= "👨‍👩‍👧‍👦 **Travelers Breakdown:**\n";
        $formattedMessage .= "  - Adults: " . ($request->input('num_adults') ?? 0) . "\n";
        $formattedMessage .= "  - Young (<16): " . ($request->input('young_16') ?? 0) . "\n";
        $formattedMessage .= "  - Kids (<11): " . ($request->input('kids_11') ?? 0) . "\n";
        $formattedMessage .= "  - Babies (<2): " . ($request->input('babies_2') ?? 0) . "\n\n";

        // Interests
        $formattedMessage .= "🌍 **Client Interests:** " . (empty($request->input('interests')) ? "N/A" : implode(", ", $request->input('interests'))) . "\n\n";

        // Agency details
        $formattedMessage .= "🏢 **Agency Name:** " . $request->input('agency_name') . "\n";
        $formattedMessage .= "👤 **Agent Name:** " . $request->input('agent_name') . "\n";
        $formattedMessage .= "📧 **Email:** " . $request->input('email') . "\n";
        $formattedMessage .= "📞 **WhatsApp Contact:** " . $request->input('whatsapp') . "\n";

        // Comments
        $formattedMessage .= "📝 **Comments:** " . ($request->input('comments') ?? "N/A") . "\n";

        // Status
        $formattedMessage .= "\n📌 **Status:** New\n";
        $formattedMessage .= "\n----------------------------------------\n";

        // Save booking in the database
        $booking = Booking::create([
            'template_id' => null,
            'message' => $formattedMessage,
            'assignee_id' => null, // Assign later if needed
        ]);

        $runningJob = RunningJob::create([
            'booking_id' => $booking->id,
        ]);

        return redirect()->back()->with('success', __('form_1.messages.success', [], app()->getLocale()));
    }

    public function form_2_store(Request $request)
    {
        // Validate request data
        $request->validate([
            'group_composition' => 'required|string|max:255',
            'num_travelers' => 'required|integer|min:1',
            'num_adults' => 'nullable|integer|min:0',
            'young_16' => 'nullable|integer|min:0',
            'kids_11' => 'nullable|integer|min:0',
            'babies_2' => 'nullable|integer|min:0',
            'num_days' => 'required|integer|min:1',
            'departure_date' => 'required|date',
            'flight_tickets' => 'required|string|in:Yes,No',
            'interests' => 'nullable|array',
            'accommodation' => 'required|string|max:255',
            'where_at' => 'required|string|max:255',
            'accompaniment' => 'required|string|max:255',
            'nationality' => 'required|string|max:255',
            'client_name' => 'required|string|max:255',
            'whatsapp' => 'required|string',
            'email' => 'required|email',
            'whatsapp_auth' => 'required|string|in:Yes,No',
            'comments' => 'nullable|string',
        ]);

        // Format the message with all booking details
        $formattedMessage = "📌 **New Booking Request**\n\n";
        $formattedMessage .= "----------------------------------------\n\n";
        $formattedMessage .= "👤 **Group Composition:** " . $request->input('group_composition') . "\n";
        $formattedMessage .= "👥 **Number of Travelers:** " . $request->input('num_travelers') . "\n";
        $formattedMessage .= "📅 **Departure Date:** " . $request->input('departure_date') . "\n";
        $formattedMessage .= "📆 **Trip Duration:** " . $request->input('num_days') . " days\n";
        $formattedMessage .= "🛫 **Flight Tickets Purchased:** " . $request->input('flight_tickets') . "\n";
        $formattedMessage .= "🏨 **Accommodation Type:** " . $request->input('accommodation') . "\n";
        $formattedMessage .= "📍 **Current Planning Stage:** " . $request->input('where_at') . "\n";
        $formattedMessage .= "🚗 **Accompaniment Type:** " . $request->input('accompaniment') . "\n";
        $formattedMessage .= "🌎 **Nationality:** " . $request->input('nationality') . "\n\n";

        // Travelers breakdown
        $formattedMessage .= "👨‍👩‍👧‍👦 **Travelers Breakdown:**\n";
        $formattedMessage .= "  - Adults: " . ($request->input('num_adults') ?? 0) . "\n";
        $formattedMessage .= "  - Young (<16): " . ($request->input('young_16') ?? 0) . "\n";
        $formattedMessage .= "  - Kids (<11): " . ($request->input('kids_11') ?? 0) . "\n";
        $formattedMessage .= "  - Babies (<2): " . ($request->input('babies_2') ?? 0) . "\n\n";

        // Interests
        $formattedMessage .= "🌍 **Client Interests:** " . (empty($request->input('interests')) ? "N/A" : implode(", ", $request->input('interests'))) . "\n\n";

        // Agency details
        $formattedMessage .= "🏢 **Client Name:** " . $request->input('client_name') . "\n";
        $formattedMessage .= "📧 **Email:** " . $request->input('email') . "\n";
        $formattedMessage .= "📞 **WhatsApp Contact:** " . $request->input('whatsapp') . "\n";
        $formattedMessage .= "✅ **WhatsApp Authorization:** " . $request->input('whatsapp_auth') . "\n";

        // Comments
        $formattedMessage .= "📝 **Comments:** " . ($request->input('comments') ?? "N/A") . "\n";

        // Status
        $formattedMessage .= "\n📌 **Status:** New\n";
        $formattedMessage .= "\n----------------------------------------\n";

        // Save booking in the database
        $booking = Booking::create([
            'template_id' => null,
            'message' => $formattedMessage,
            'assignee_id' => null, // Assign later if needed
        ]);

        $runningJob = RunningJob::create([
            'booking_id' => $booking->id,
        ]);

        return redirect()->back()->with('success', __('form_2.messages.success', [], app()->getLocale()));
    }



    public function index()
    {
        $totalBookings = Booking::count();
        $usersAssign = User::role(['Admin', 'Travel Consultant'])
            ->select('id', 'name')
            ->get();
        $usersHandler = User::whereDoesntHave('roles', function($query) {
            $query->where('name', 'Customer');
        })->select('id', 'name')->get();
        // Get all markets for the dropdown
        $markets = \App\Models\Market::select('id', 'market')->get();

        $templates = \App\Models\Template::select('id', 'template_name')->orderBy('template_name')->get(); // Fetch all templates

        return view('bookings.index', compact('totalBookings', 'usersHandler' ,'usersAssign', 'templates', 'markets'));
    }

    public function getBookings(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $query = Booking::with([
                'runningJob.quotationAssignees',
                'runningJob.quotation:id,quotation_no,reference_no,user_id',
                'runningJob.quotation.user:id,name',
                'assignee',
                'market:id,market'
            ])
            ->when(!$user->hasRole('Super Admin') && !$user->hasRole('Admin'), function ($query) use ($user) {
                // For non-admin users, show only bookings they are assigned to
                $query->whereHas('runningJob', function ($q) use ($user) {
                    $q->where(function ($subQuery) use ($user) {
                        $subQuery->where('assignee_id', $user->id)
                                ->orWhereHas('quotationAssignees', function ($qa) use ($user) {
                                    $qa->where('user_id', $user->id);
                                });
                    });
                });
            })
            ->when($user->hasRole('Admin') && !$user->hasRole('Super Admin'), function ($query) use ($user) {
                // For Admin users (not Super Admin), filter by their assigned markets
                $userMarkets = $user->markets->pluck('id')->toArray();

                if (!empty($userMarkets)) {
                    $query->whereIn('market_id', $userMarkets);
                } else {
                    // If admin has no markets assigned, show no bookings
                    $query->where('id', 0);
                }
            })
            ->orderBy('created_at', 'desc');

            // Apply filters
            // Handle global search (Reference ID)
            if ($request->has('search') && !empty($request->search['value'])) {
                $searchValue = $request->search['value'];
                $query->where(function($q) use ($searchValue) {
                    $q->where('reference_id', 'like', '%' . $searchValue . '%')
                      ->orWhereHas('runningJob.quotation', function($subQ) use ($searchValue) {
                          $subQ->where('reference_no', 'like', '%' . $searchValue . '%');
                      });
                });
            }

            // Filter by reference_id (explicit filter)
            if ($request->filled('reference_id')) {
                $query->where(function($q) use ($request) {
                    $q->where('reference_id', 'like', '%' . $request->reference_id . '%')
                      ->orWhereHas('runningJob.quotation', function($subQ) use ($request) {
                          $subQ->where('reference_no', 'like', '%' . $request->reference_id . '%');
                      });
                });
            }

            if ($request->filled('assignee_id')) {
                $query->where('assignee_id', $request->assignee_id);
            }

            // Filter by quotation assignee (multi-select)
            if ($request->has('quotation_assignee') && !empty($request->quotation_assignee)) {
                $query->whereHas('runningJob.quotationAssignees', function ($q) use ($request) {
                    $q->whereIn('user_id', (array)$request->quotation_assignee);
                });
            }

            if ($request->filled('status_01')) {
                $query->where('status_01', $request->status_01);
            }

            if ($request->filled('status_02')) {
                $query->where('status_02', $request->status_02);
            }

            return DataTables::of($query)
                ->addColumn('quotation_assignees', function ($booking) {
                    return $booking->runningJob?->quotationAssignees->map(function ($user) {
                        return ['id' => $user->id, 'name' => $user->name];
                    }) ?? [];
                })
                ->addColumn('quotation_user_id', function ($booking) {
                    return $booking->runningJob?->quotation?->user_id ?? null;
                })
                ->addColumn('quotation_no', function ($booking) {
                    return $booking->runningJob?->quotation?->quotation_no ?? 'N/A';
                })
                ->addColumn('reference_id', function ($booking) {
                    return $booking->runningJob?->quotation?->reference_no ?? $booking->reference_id ?? 'N/A';
                })
                ->addColumn('market', function ($booking) {
                    return $booking->market?->market ?? '-';
                })
                ->editColumn('message', function ($booking) {
                    return nl2br(e($booking->message));
                })
                ->editColumn('running_job_id', function ($booking) {
                    return $booking->runningJob?->id ?? 'N/A';
                })
                ->rawColumns(['message'])
                ->make(true);
        }
    }

    public function updateStatus(Request $request, Booking $booking)
    {
        $request->validate([
            'status' => 'required|in:new,review,followup,rejected,done'
        ]);

        $booking->update(['status' => $request->status]);

        return response()->json(['message' => 'Status updated successfully.']);
    }

    public function updateAssignee(Request $request, $id)
    {
        try {
            $booking = Booking::findOrFail($id);
            $currentUser = auth()->user();

            // Check if user has permission to update assignee
            // Allow Super Admin, Admin, or agents who are currently assigned to this booking
            $canUpdateAssignee = false;

            if ($currentUser->hasAnyRole(['Super Admin', 'Admin'])) {
                $canUpdateAssignee = true;
            } elseif ($currentUser->hasRole('Agent') && $booking->assignee_id == $currentUser->id) {
                // Agent who is currently assigned to this booking can update assignee
                $canUpdateAssignee = true;
            }

            if (!$canUpdateAssignee) {
                return response()->json([
                    'message' => 'You do not have permission to update the booking assignee.'
                ], 403);
            }

            $assigneeId = $request->input('assignee_id');

            // Update the assignee
            $booking->update([
                'assignee_id' => $assigneeId == 0 ? null : $assigneeId
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Booking assignee updated successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Booking $booking)
    {
        $booking->delete();

        return response()->json(['message' => 'Booking deleted successfully.']);
    }

    public function updateStatus01(Request $request, $id)
    {
        $request->validate(['status_01' => 'required|string']);
        Booking::findOrFail($id)->update(['status_01' => $request->status_01]);
        return response()->json(['message' => 'Status 01 updated']);
    }

    public function updateStatus02(Request $request, $id)
    {
        $request->validate([
            'status_02' => 'required|string',
            'status_02_comment' => 'nullable|string',
        ]);
        Booking::findOrFail($id)->update([
            'status_02' => $request->status_02,
            'status_02_comment' => $request->status_02_comment,
        ]);
        return response()->json(['message' => 'Status 02 updated']);
    }

    public function updateStatus03(Request $request, $id)
    {
        $request->validate(['status_03' => 'nullable|array']);
        Booking::findOrFail($id)->update(['status_03' => $request->status_03]);
        return response()->json(['message' => 'Status 03 updated']);
    }

    public function addStatus03Item(Request $request, $id)
    {
        $request->validate(['item' => 'required|string']);
        $booking = Booking::findOrFail($id);
        $items = $booking->status_03 ? json_decode($booking->status_03, true) : [];
        $items[] = $request->item;
        $booking->status_03 = json_encode(array_unique($items));
        $booking->save();
        return response()->json(['message' => 'Custom item added']);
    }


    /**
     * Show the form for creating a new booking.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Get all templates for the dropdown
        $templates = \App\Models\Template::select('id', 'name')->orderBy('name')->get();

        // Get all markets for the dropdown
        $markets = \App\Models\Market::select('id', 'market')->orderBy('market')->get();

        return view('bookings.create', compact('templates', 'markets'));
    }

    /**
     * Update the reference ID for a booking
     */
    public function updateReferenceId(Request $request, $id)
    {
        $request->validate([
            'reference_id' => 'required|string|max:255',
        ]);

        $runningJob = RunningJob::findOrFail($id);

        // Find the associated booking
        $booking = $runningJob->booking;

        if ($booking) {
            // Update the booking's reference_id
            $booking->reference_id = $request->reference_id;
            $booking->save();

            return response()->json([
                'success' => true,
                'message' => 'Reference ID updated successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Booking not found'
        ], 404);
    }

    /**
     * Upload air ticket files for a booking
     */
    public function uploadAirTickets(Request $request, $id)
    {
        $request->validate([
            'air_ticket_files.*' => 'required|file|mimes:jpg,jpeg,png,pdf|max:10240', // 10MB max
        ]);

        try {
            $booking = Booking::findOrFail($id);
            $currentFiles = $booking->air_ticket_files ?? [];
            $uploadedFiles = [];

            if ($request->hasFile('air_ticket_files')) {
                foreach ($request->file('air_ticket_files') as $file) {
                    $fileName = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                    $filePath = $file->storeAs('bookings/air_tickets', $fileName, 'public');

                    $uploadedFiles[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'file_path' => $filePath,
                        'file_size' => $file->getSize(),
                        'uploaded_at' => now()->toDateTimeString(),
                    ];
                }
            }

            // Merge with existing files
            $allFiles = array_merge($currentFiles, $uploadedFiles);
            $booking->update(['air_ticket_files' => $allFiles]);

            return response()->json([
                'success' => true,
                'message' => 'Air ticket files uploaded successfully',
                'files' => $uploadedFiles
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload air ticket files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload passport files for a booking
     */
    public function uploadPassports(Request $request, $id)
    {
        $request->validate([
            'passport_files.*' => 'required|file|mimes:jpg,jpeg,png,pdf|max:10240', // 10MB max
        ]);

        try {
            $booking = Booking::findOrFail($id);
            $currentFiles = $booking->passport_files ?? [];
            $uploadedFiles = [];

            if ($request->hasFile('passport_files')) {
                foreach ($request->file('passport_files') as $file) {
                    $fileName = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                    $filePath = $file->storeAs('bookings/passports', $fileName, 'public');

                    $uploadedFiles[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'file_path' => $filePath,
                        'file_size' => $file->getSize(),
                        'uploaded_at' => now()->toDateTimeString(),
                    ];
                }
            }

            // Merge with existing files
            $allFiles = array_merge($currentFiles, $uploadedFiles);
            $booking->update(['passport_files' => $allFiles]);

            return response()->json([
                'success' => true,
                'message' => 'Passport files uploaded successfully',
                'files' => $uploadedFiles
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload passport files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an air ticket file
     */
    public function deleteAirTicket(Request $request, $id, $fileIndex)
    {
        try {
            $booking = Booking::findOrFail($id);
            $files = $booking->air_ticket_files ?? [];

            if (isset($files[$fileIndex])) {
                // Delete file from storage
                $filePath = $files[$fileIndex]['file_path'];
                if (Storage::disk('public')->exists($filePath)) {
                    Storage::disk('public')->delete($filePath);
                }

                // Remove from array
                unset($files[$fileIndex]);
                $files = array_values($files); // Re-index array

                $booking->update(['air_ticket_files' => $files]);

                return response()->json([
                    'success' => true,
                    'message' => 'Air ticket file deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete air ticket file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a passport file
     */
    public function deletePassport(Request $request, $id, $fileIndex)
    {
        try {
            $booking = Booking::findOrFail($id);
            $files = $booking->passport_files ?? [];

            if (isset($files[$fileIndex])) {
                // Delete file from storage
                $filePath = $files[$fileIndex]['file_path'];
                if (Storage::disk('public')->exists($filePath)) {
                    Storage::disk('public')->delete($filePath);
                }

                // Remove from array
                unset($files[$fileIndex]);
                $files = array_values($files); // Re-index array

                $booking->update(['passport_files' => $files]);

                return response()->json([
                    'success' => true,
                    'message' => 'Passport file deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete passport file: ' . $e->getMessage()
            ], 500);
        }
    }
}
