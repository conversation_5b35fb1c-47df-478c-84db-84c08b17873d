<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Profile;
use App\Models\User;
use App\Models\Quotation;
use App\Models\Market;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;

class ProfileController extends Controller
{
  public function index()
  {
    return view('profile.index');
  }

  public function show($id)
  {
    $user = Auth::user();
    $profile = Auth::user()->profile;

    $userId = Auth::id();

    $savedQuotationsCount = Quotation::where('user_id', $userId)
        ->where('save_type', 'save')
        ->count();

    $confirmedQuotationsCount = Quotation::where('user_id', $userId)
        ->where('save_type', 'confirm')
        ->count();

    return view('profile.show', compact('user', 'profile', 'savedQuotationsCount', 'confirmedQuotationsCount'));
  }

  public function create()
  {
    $user = Auth::user();
    $profile = Auth::user()->profile;
    $roles = Role::all();
    $markets = Market::all();

    return view('profile.create', compact('user', 'profile', 'roles', 'markets'));
  }

  public function store(Request $request)
  {
    try {
      // Validate the input
      $request->validate([
        'firstname' => 'required|string|max:255',
        'lastname' => 'nullable|string|max:255',
        'address' => 'nullable|string',
        'email' => 'required|email|unique:users,email',
        'status' => 'required|in:1,2',
        'phone' => 'nullable|string',
        'password' => 'required|confirmed|min:8', // Password must match password_confirmation
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
      ]);

      // Create the new user
      $user = User::create([
        'name' => $request->input('firstname'),
        'email' => $request->input('email'),
        'password' => Hash::make($request->input('password')),
        'status' => $request->input('status'),
      ]);

      if ($request->has('roles') && $request->input('roles')) {
        $user->assignRole($request->input('roles')); // Assign selected roles to the user
      }

      // Handle market assignments for Admin users
      if ($request->has('markets') && $request->input('markets') && $user->hasRole('Admin')) {
        $user->markets()->sync($request->input('markets'));
      }

      // Handle profile image upload
      $image = null;
      if ($request->hasFile('image')) {
        // Store new image
        $imageName = time() . '.' . $request->image->extension();
        $request->image->storeAs('public/user', $imageName);
        $image = Storage::url('user/' . $imageName);
      }

      // Create a new profile for the user
      $user->profile()->create([
        'firstname' => $request->input('firstname'),
        'lastname' => $request->input('lastname'),
        'address' => $request->input('address'),
        'phone' => $request->input('phone'),
        'image' => $image,
      ]);

      // Redirect back with success message
      return redirect()
        ->back()
        ->with('success', 'User created successfully!');
    } catch (\Illuminate\Validation\ValidationException $e) {
      // Validation failed, redirect back with validation errors and modal flag
      return redirect()
        ->back()
        ->withErrors($e->validator) // Handle validation errors
        ->withInput() // Keep the old input
        ->with('error', 'Failed to create user! Please correct the errors.')
        ->with('showModalAddUser', true); // Flag to reopen the modal on error
    }
  }

  public function edit($id)
  {
    // Find the user by the provided ID or fail if the user doesn't exist
    $user = User::findOrFail($id);
    $profile = $user->profile;
    $roles = Role::all(); // Retrieve all available roles
    $userRoles = $user->roles->pluck('name')->toArray(); // Get assigned roles for the user
    $markets = Market::all(); // Get all markets
    $userMarkets = $user->markets->pluck('id')->toArray(); // Get assigned markets for the user

    // Return the view with the user and profile data
    return view('profile.edit', compact('profile', 'user', 'roles', 'userRoles', 'markets', 'userMarkets'));
  }

  public function update(Request $request)
  {
    try {
      $request->validate([
        'firstname' => 'required|string|max:255',
        'lastname' => 'required|string|max:255',
        'address' => 'required|string',
        'signature' => 'required|string',
        'email' => 'required|email',
        'status' => 'required|in:1,2',
        'phone' => 'required|string',
        'password' => 'nullable|confirmed|min:8', // Password must match password_confirmation
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
      ]);

      $user = User::findOrFail(Auth::user()->id);
      $image = $user->profile->image ?? null;

      // Handle profile image upload
      if ($request->hasFile('image')) {
        // Delete old image if exists
        if ($image) {
          Storage::delete('public/user/' . $user->image);
        }

        // Store new image
        $imageName = time() . '.' . $request->image->extension();
        $request->image->storeAs('public/user', $imageName);
        $image = Storage::url('user/' . $imageName);
      }

      // Update user details
      // Use updateOrCreate to either update or create a profile for the user
      $user->profile()->updateOrCreate(
        ['user_id' => $user->id], // Condition: If the profile exists for this user
        [
          'firstname' => $request->input('firstname'),
          'lastname' => $request->input('lastname'),
          'address' => $request->input('address'),
          'signature' => $request->input('signature'),
          'email' => $request->input('email'),
          'phone' => $request->input('phone'),
          'image' => $image, // Use the same image if not updated
        ]
      );

      if ($request->has('roles') && $request->input('roles')) {
        $user->syncRoles($request->input('roles'));
      }


      $newUser = [
        'name' => $request->input('firstname'),
        'status' => $request->input('status'),
      ];
      // Check if password is being updated
      if ($request->filled('password')) {
        $newUser['password'] = Hash::make($request->input('password'));
      }

      $user->update($newUser);
      return redirect()
        ->back()
        ->with('success', 'User updated successfully!');
    } catch (\Illuminate\Validation\ValidationException $e) {
      // Validation failed, redirect back with validation errors, custom error message, and modal flag
      return redirect()
        ->back()
        ->withErrors($e->validator) // This will handle validation errors
        ->withInput() // Keep the old input
        ->with('error', 'Failed to update user! Please correct the errors.')
        ->with('showModal', true); // This flag will be used to display the modal
    }
  }

  public function a_update($id, Request $request)
  {
    try {
      $request->validate([
        'firstname' => 'required|string|max:255',
        'lastname' => 'nullable|string|max:255',
        'address' => 'nullable|string',
        'status' => 'required|in:1,2',
        'phone' => 'nullable|string',
        'password' => 'nullable|confirmed|min:8', // Password must match password_confirmation
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
      ]);

      $user = User::findOrFail($id);
      $image = $user->profile->image ?? null;

      // Handle profile image upload
      if ($request->hasFile('image')) {
        // Delete old image if exists
        if ($image) {
          Storage::delete('public/user/' . $user->image);
        }

        // Store new image
        $imageName = time() . '.' . $request->image->extension();
        $request->image->storeAs('public/user', $imageName);
        $image = Storage::url('user/' . $imageName);
      }

      // Update user details
      // Use updateOrCreate to either update or create a profile for the user
      $user->profile()->updateOrCreate(
        ['user_id' => $user->id], // Condition: If the profile exists for this user
        [
          'firstname' => $request->input('firstname'),
          'lastname' => $request->input('lastname'),
          'address' => $request->input('address'),
          'email' => $request->input('email'),
          'phone' => $request->input('phone'),
          'image' => $image, // Use the same image if not updated
        ]
      );

      $user->syncRoles($request->input('roles'));


      // Handle market assignments for Admin users
      if ($user->hasRole('Admin')) {
        if ($request->has('markets') && $request->input('markets')) {
          $user->markets()->sync($request->input('markets'));
        } else {
          $user->markets()->detach(); // Remove all markets if none selected
        }
      }

      $newUser = [
        'status' => $request->input('status'),
      ];
      // Check if password is being updated
      if ($request->filled('password')) {
        $newUser['password'] = Hash::make($request->input('password'));
      }

      $user->update($newUser);
      return redirect()
        ->back()
        ->with('success', 'User updated successfully!');
    } catch (\Illuminate\Validation\ValidationException $e) {
      // Validation failed, redirect back with validation errors, custom error message, and modal flag
      return redirect()
        ->back()
        ->withErrors($e->validator) // This will handle validation errors
        ->withInput() // Keep the old input
        ->with('error', 'Failed to update user! Please correct the errors.')
        ->with('showModalEditUser', true); // This flag will be used to display the modal
    }
  }

  public function getUsers(Request $request)
  {
    if ($request->ajax()) {
      // Capture the relevant parameters from the DataTables request
      $searchValue = $request->input('search.value'); // Search value
      $orderColumnIndex = $request->input('order.0.column'); // Column index for sorting
      $orderDirection = $request->input('order.0.dir'); // Sort direction (asc/desc)
      $columns = $request->input('columns'); // Columns from DataTables

      // Mapping column index to the corresponding table column names
      $orderColumn = $columns[$orderColumnIndex]['data'];

      // Fetch the users with their profiles, applying filters and sorting
      $users = User::with('profile')
        ->whereDoesntHave('roles', function($query) {
            $query->where('name', 'Customer');
        })
        ->when($searchValue, function ($query) use ($searchValue) {
          // Apply search to relevant fields
          return $query
            ->whereHas('profile', function ($q) use ($searchValue) {
              $q->where('firstname', 'like', "%{$searchValue}%")->orWhere('lastname', 'like', "%{$searchValue}%");
            })
            ->orWhere('email', 'like', "%{$searchValue}%");
        })
        ->orderBy($orderColumn, $orderDirection) // Apply sorting
        ->paginate($request->input('length'), ['*'], 'page', $request->input('start') / $request->input('length') + 1);

      // Modify the result to include profile data (telephone and status)
      $usersData = $users->map(function ($user) {
        return [
          'id' => $user->id,
          'firstname' => $user->profile ? $user->profile->firstname : '',
          'lastname' => $user->profile ? $user->profile->lastname : '',
          'email' => $user->email,
          'phone' => $user->profile ? $user->profile->phone : 'N/A',
          'status' => $user->status ? config('custom.user.status')[$user->status] : 'Inactive', // Adjust this as needed
          'role' => $user->getRoleNames()->first(),
        ];
      });

      // Return the JSON response in the format DataTables expects
      return response()->json([
        'draw' => intval($request->input('draw')), // Draw counter from DataTables
        'recordsTotal' => $users->total(), // Total number of records (without filtering)
        'recordsFiltered' => $users->total(), // Total number of records after filtering
        'data' => $usersData, // Data for the current page
      ]);
    }
  }

  public function destroy($id)
  {
    try {
      // Find the user by ID
      $user = User::findOrFail($id);

      // Check if the user has a profile
      if ($user->profile) {
        // Delete the profile
        $user->profile->delete();
      }

      // Delete the user
      $user->delete();

      return response()->json(['success' => true, 'message' => 'User and profile deleted successfully!']);
    } catch (\Exception $e) {
      return response()->json(['error' => true, 'message' => 'An error occurred while deleting the user.']);
    }
  }
}
