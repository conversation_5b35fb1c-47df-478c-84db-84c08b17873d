@extends('layouts/contentNavbarLayout')

@section('title', 'Confirmation Report')

@section('vendor-style')

@endsection

@section('vendor-script')


@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/quotation.css') }}">
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4 text-center blue-heading">HOTEL CONFIRMATION REPORT – {{ $quotation['reference_no'] }}</h3>
            <h4 class="mb-3 text-center">{{ count($quotation['days']) }} {{ __('labels.days') }},
                {{ count($quotation['days']) - 1 }} {{ __('labels.nights') }} in Sri Lanka</h4>
            <div class="table-responsive text-nowrap">
                <table class="table mb-3 bg-white table-sm">
                    <thead class="table-dark">
                        <tr>
                            <th class="text-white">{{ __('labels.hotel') }}</th>
                            <th class="text-white">{{ __('labels.checkin_checkout') }}</th>
                            <th class="text-white">{{ __('labels.nights') }}</th>
                            <th class="text-white">{{ __('labels.room_category') }}</th>
                            <th class="text-white">{{ __('labels.hotel_category') }}</th>
                            <th class="text-white">{{ __('labels.meal_type') }}</th>
                            <th class="text-white">{{ 'Status' }}</th>
                        </tr>
                    </thead>
                    <tbody class="table-border-bottom-0">
                        @foreach ($hotels as $hotel)
                            <tr>
                                <td><span>{{ $hotel['hotel_name'] }}</span><br>
                                    <small>{{ $hotel['city_name'] }}</small>
                                </td>
                                <td><span>{{ $hotel['checkin_date'] }} - {{ $hotel['checkout_date'] }} </span></td>
                                <td>{{ $hotel['nights'] }}</td>
                                <td>
                                    @foreach ($hotel['rooms_count'] as $room)
                                        <span>{{ $hotel['room_category'] }} - {{ $room }}</span><br>
                                    @endforeach
                                </td>
                                <td>{{ $hotel['hotel_category'] }}</td>
                                <td>{{ $hotel['meal_type'] }}</td>
                                <td>
                                    @if (hasAnyRole(['Foreign Agent', 'Foreign Agency']))
                                        {{-- Show only status text for Foreign Agents --}}
                                        <span
                                            class="badge
                                            @if (!isset($hotelStatus[$hotel['hotel_id']]) || $hotelStatus[$hotel['hotel_id']] == 'Pending') bg-warning
                                            @else
                                                bg-success @endif
                                        ">
                                            {{ $hotelStatus[$hotel['hotel_id']] ?? 'Pending' }}
                                        </span>
                                    @else
                                        {{-- Show select box for other roles --}}
                                        <select name="hotel_confirmation_status"
                                            class="form-select hotel_confirmation_status"
                                            data-hotel-id="{{ $hotel['hotel_id'] }}"
                                            data-quotation-id="{{ $quotation['id'] }}">

                                            <option value="Pending"
                                                {{ !isset($hotelStatus[$hotel['hotel_id']]) || $hotelStatus[$hotel['hotel_id']] == 'Pending' ? 'selected' : '' }}>
                                                Pending
                                            </option>

                                            <option value="Confirmed"
                                                {{ isset($hotelStatus[$hotel['hotel_id']]) && $hotelStatus[$hotel['hotel_id']] == 'Confirmed' ? 'selected' : '' }}>
                                                Confirmed
                                            </option>

                                        </select>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <a class="btn text-primary" target="_default"
                href={{ '/admin/quotation/custom-confirmation-report-pdf/' . $quotation['id'] . '/' . $quotation['quotation_no'] }}>
                <i class="bx bx-download me-1"></i> Pdf Download
            </a>
        </div>
    </div>

@endsection



@section('page-script')
    <script src="{{ asset('assets/js/quotation.js') }}"></script>

    <script>
        $(document).ready(function() {
            $('.hotel_confirmation_status').change(function() {
                let hotelId = $(this).data('hotel-id');
                let quotationId = $(this).data('quotation-id');
                let status = $(this).val();

                $.ajax({
                    url: "{{ route('hotel.confirmation.update') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        hotel_id: hotelId,
                        quotation_id: quotationId,
                        status: status
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: "Success",
                                text: response.message,
                                icon: "success",
                                confirmButtonText: "OK"
                            });
                        } else {
                            Swal.fire({
                                title: "Error",
                                text: "Failed to update status.",
                                icon: "error",
                                confirmButtonText: "OK"
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: "Error",
                            text: "An error occurred. Please try again.",
                            icon: "error",
                            confirmButtonText: "OK"
                        });
                    }
                });
            });
        });
    </script>
@endsection
