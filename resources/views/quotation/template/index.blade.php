@extends('layouts/contentNavbarLayout')

@section('title', 'Template Index')

@section('vendor-style')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css">
@endsection

@section('vendor-script')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>
@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/quotation.css') }}">
    <style>
        /* Custom slider styling */
        .noUi-target {
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            box-shadow: none;
            height: 8px;
        }

        .noUi-connect {
            background: #210070;
        }

        .noUi-handle {
            background: #fff;
            border: 2px solid #210070;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            width: 18px;
            height: 18px;
        }

        .noUi-handle:before,
        .noUi-handle:after {
            display: none;
        }

        .noUi-horizontal .noUi-handle {
            width: 20px;
            height: 20px;
            top: -8px;
            right: -10px;
        }

        .noUi-tooltip {
            background: #333;
            border: none;
            border-radius: 3px;
            color: #fff;
            font-size: 12px;
            padding: 4px 8px;
        }

        /* Slider container spacing */
        #filterDays,
        #filterPrice {
            margin: 10px 0;
            height: 5px;
        }
    </style>
@endsection

@section('content')
    <div class="card mb-4">
        <div class="card-header border-bottom">
            <h5 class="card-title">Tour Templates</h5>

            <div class="mb-3 row">
                <div class="mb-3 col-3">
                    <label for="filterDays">Filter by Days: <span id="daysValue">0 - 20</span></label>
                    <div id="filterDays" class="mt-2"></div>
                </div>
                <div class="mb-3 col-3">
                    <label for="filterPax">Filter by Pax:</label>
                    <select id="filterPax" class="form-select">
                        <option value="">All Pax</option>
                        <option value="1-2">1-2 Persons</option>
                        <option value="3-5">3-5 Persons</option>
                        <option value="6-10">6-10 Persons</option>
                        <option value="10+">10+ Persons</option>
                    </select>
                </div>
                <div class="mb-3 col-3">
                    <label for="filterPrice">Filter by Price: <span id="priceValue">$0 - $20,000</span></label>
                    <div id="filterPrice" class="mt-2"></div>
                </div>
                <div class="mb-3 col-3">
                    <label for="filterMonth">Filter by Month:</label>
                    <select id="filterMonth" class="form-select">
                        <option value="">All Months</option>
                        <option value="1">January</option>
                        <option value="2">February</option>
                        <option value="3">March</option>
                        <option value="4">April</option>
                        <option value="5">May</option>
                        <option value="6">June</option>
                        <option value="7">July</option>
                        <option value="8">August</option>
                        <option value="9">September</option>
                        <option value="10">October</option>
                        <option value="11">November</option>
                        <option value="12">December</option>
                    </select>
                </div>
            </div>

            @if (hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant']))
                <!--button class="btn add-new btn-primary float-end" id="addNewTemplate" type="button"
                                                                            data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddTemplate"><span><i
                                                                                    class="bx bx-plus bx-sm me-0 me-sm-2"></i><span class="d-none d-sm-inline-block">Add New
                                                                                    Template</span></span></button-->
            @endif
        </div>
    </div>

    <div class="row g-6" id="template-container">
        @foreach ($templates as $template)
            <div class="col-xl-3 col-md-6 template-card" data-days="{{ $template['days_count'] }}"
                data-price="{{ $template['total_price'] ?? 0 }}" data-pax="{{ $template['pax_count'] ?? 0 }}"
                data-month="{{ $template['arrival_month'] ?? 0 }}">
                <div class="border rounded shadow-none card">
                    <div class="px-5 card-body">
                        <div class="mt-3 mb-5 text-center">
                            <img src="{{ (new \App\Models\Template())->getImageUrl($template['image']) }}" alt=""
                                width="100%">
                        </div>
                        <h4 class="mb-1 text-center card-title text-capitalize">{{ $template['template_name'] }}</h4>

                        <div class="d-flex justify-content-between mb-3">
                            <span class="badge bg-label-primary rounded-pill">
                                <i class="bx bx-calendar me-1"></i>
                                {{ $template['days_count'] }} Days
                            </span>
                            <span class="badge bg-label-info rounded-pill">
                                <i class="bx bx-user me-1"></i>
                                {{ $template['pax_count'] ?? 0 }} Pax
                            </span>
                        </div>

                        <div class="d-flex justify-content-between mb-4">
                            @if ($template['arrival_month'])
                                <span class="badge bg-label-success rounded-pill">
                                    <i class="bx bx-calendar-event me-1"></i>
                                    {{ date('F', mktime(0, 0, 0, $template['arrival_month'], 1)) }}
                                </span>
                            @endif
                            @if ($template['show'] == 1)
                                <span class="badge bg-label-warning rounded-pill">
                                    <i class="bx bx-show me-1"></i>
                                    Front Page
                                </span>
                            @endif
                        </div>

                        <div class="text-center h-px-50 mb-3">
                            <div class="d-flex justify-content-center">
                                <sup class="mt-2 mb-0 h6 text-body pricing-currency me-1">$</sup>
                                <h1 class="mb-0 text-primary">{{ $template['total_price'] ?? 0 }}</h1>
                                <sub class="mt-auto mb-1 h6 text-body pricing-duration ms-1">/pp</sub>
                            </div>
                        </div>

                        <a class="mb-1 btn btn-label-success d-grid w-100 use-template"
                            data-id="{{ $template['quotation_id'] }}">Use This</a>
                        @if (hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant']))
                            <button type="button" class="mb-1 btn btn-icon delete-record btn-label-danger me-2 w-100"
                                data-id="{{ $template['template_id'] }}">
                                Delete This
                            </button>
                            <button class="mb-1 btn btn-icon edit-template btn-label-warning w-100"
                                data-id="{{ $template['template_id'] }}" title="Edit" data-bs-toggle="offcanvas"
                                data-bs-target="#offcanvasEditTemplate">
                                Edit This
                            </button>
                        @endif
                        <button class="mb-1 btn btn-icon btn-label-secondary w-100" title="Preview"
                            onclick="openPrivateLink('{{ $template['token'] }}')">
                            Preview This
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Offcanvas for Editing Templates -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEditTemplate"
        aria-labelledby="offcanvasEditTemplateLabel">
        <div class="offcanvas-header border-bottom">
            <h5 id="offcanvasEditTemplateLabel" class="offcanvas-title">Edit Template</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">
            <!-- Edit template form will load here dynamically -->
        </div>
    </div>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/admin.js') }}"></script>
    <script>
        function openPrivateLink(id) {
            const privateLink = `{{ url('admin/quotations/preview') }}/${id}`;
            window.open(privateLink, '_blank'); // Opens in a new tab
        }
        $(document).ready(function() {
            // Initialize sliders
            let daysSlider, priceSlider;
            let daysRange = [0, 20];
            let priceRange = [0, 20000];

            // Initialize Days slider
            daysSlider = document.getElementById('filterDays');
            noUiSlider.create(daysSlider, {
                start: [0, 20],
                connect: true,
                range: {
                    'min': 0,
                    'max': 20
                },
                step: 1,
                format: {
                    to: function(value) {
                        return Math.round(value);
                    },
                    from: function(value) {
                        return Number(value);
                    }
                }
            });

            // Initialize Price slider
            priceSlider = document.getElementById('filterPrice');
            noUiSlider.create(priceSlider, {
                start: [0, 20000],
                connect: true,
                range: {
                    'min': 0,
                    'max': 20000
                },
                step: 100,
                format: {
                    to: function(value) {
                        return Math.round(value);
                    },
                    from: function(value) {
                        return Number(value);
                    }
                }
            });

            // Update display values and filter when sliders change
            daysSlider.noUiSlider.on('update', function(values, handle) {
                daysRange = [parseInt(values[0]), parseInt(values[1])];
                document.getElementById('daysValue').innerHTML = daysRange[0] + ' - ' + daysRange[1];
                filterTemplates();
            });

            priceSlider.noUiSlider.on('update', function(values, handle) {
                priceRange = [parseInt(values[0]), parseInt(values[1])];
                document.getElementById('priceValue').innerHTML = '$' + priceRange[0].toLocaleString() +
                    ' - $' + priceRange[1].toLocaleString();
                filterTemplates();
            });

            // Filter functionality for select boxes
            $('#filterPax, #filterMonth').on('change', function() {
                filterTemplates();
            });

            function filterTemplates() {
                const paxFilter = $('#filterPax').val();
                const monthFilter = $('#filterMonth').val();

                $('.template-card').each(function() {
                    let show = true;
                    const days = parseInt($(this).data('days'));
                    const price = parseFloat($(this).data('price'));
                    const pax = parseInt($(this).data('pax'));
                    const month = parseInt($(this).data('month'));

                    // Filter by days using slider range
                    if (show && (days < daysRange[0] || days > daysRange[1])) {
                        show = false;
                    }

                    // Filter by price using slider range
                    if (show && (price < priceRange[0] || price > priceRange[1])) {
                        show = false;
                    }

                    // Filter by pax
                    if (paxFilter && show) {
                        if (paxFilter === '1-2' && (pax < 1 || pax > 2)) show = false;
                        else if (paxFilter === '3-5' && (pax < 3 || pax > 5)) show = false;
                        else if (paxFilter === '6-10' && (pax < 6 || pax > 10)) show = false;
                        else if (paxFilter === '10+' && pax < 10) show = false;
                    }

                    // Filter by month
                    if (monthFilter && show) {
                        if (parseInt(monthFilter) !== month) show = false;
                    }

                    // Show or hide based on filters
                    if (show) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            $(document).on('click', '.use-template', function() {
                NProgress.start();
                var id = $(this).data('id');

                // Get query parameters from URL
                var urlParams = new URLSearchParams(window.location.search);
                var sourceType = urlParams.get('source_type');
                var runningJobId = urlParams.get('running_job_id');

                // Prepare data object
                var data = {
                    "quotation_no": id
                };

                // Add source_type and running_job_id if they exist
                if (sourceType) {
                    data.source_type = sourceType;
                }
                if (runningJobId) {
                    data.running_job_id = runningJobId;
                }

                useTemplate(data, function(response) {
                    // Append query parameters to the redirect URL if they exist
                    var redirectUrl = response.redirect;
                    if (sourceType && runningJobId) {
                        // Check if the URL already has parameters
                        if (redirectUrl.includes('?')) {
                            redirectUrl +=
                                `&source_type=${sourceType}&running_job_id=${runningJobId}`;
                        } else {
                            redirectUrl +=
                                `?source_type=${sourceType}&running_job_id=${runningJobId}`;
                        }
                    }

                    window.location.href = redirectUrl;
                    NProgress.done();
                });
            });


            $(document).on('click', '.edit-template', function() {
                var templateId = $(this).data('id');
                var url = `/admin/templates/edit/${templateId}`;

                // Load edit form into the offcanvas body
                fetch(url)
                    .then((response) => response.text())
                    .then((html) => {
                        $('#offcanvasEditTemplate .offcanvas-body').html(html);
                    })
                    .catch((error) => {
                        console.error('Error loading edit form:', error);
                        alert('An error occurred while loading the edit form.');
                    });
            });

            // Delete template functionality
            $(document).on('click', '.delete-record', function(e) {
                e.preventDefault();
                var templateId = $(this).data('id');
                Swal.fire({
                    title: "Are you sure?",
                    text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Yes, delete it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `/admin/templates/${templateId}`,
                            type: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                Swal.fire("Deleted!", "The template has been deleted.",
                                    "success");
                                location.reload();
                            },
                            error: function(error) {
                                Swal.fire("Error!", "An error occurred while deleting.",
                                    "error");
                                location.reload();
                            }
                        });
                    }
                });
            });
        });
    </script>
@endsection
