<div class="table-responsive text-nowrap horizontal-container">
    <h5 class="mb-2">{{ __('labels.hotels_accommodation') }}</h5>
    <table class="table mb-3 bg-white table-sm">
        <thead class="table-dark">
            <tr>
                <th class="text-white">{{ __('labels.hotel') }}</th>
                <th class="text-white">{{ __('labels.city') }}</th>
                <th class="text-white">{{ __('labels.checkin_checkout') }}</th>
                <th class="text-white">{{ __('labels.nights') }}</th>
                <th class="text-white">{{ __('labels.room_category') }}</th>
                <th class="text-white">{{ __('labels.hotel_category') }}</th>
                <th class="text-white">{{ __('labels.meal_type') }}</th>
            </tr>
        </thead>
        <tbody class="table-border-bottom-0">
            @foreach ($hotels as $hotel)
                <tr>
                    <td><span>{{ $hotel['hotel_name'] }}</span>
                    </td>
                    <td><span>{{ $hotel['city_name'] }}</span>
                    </td>
                    <td><span>{{ \App\Helpers\DateHelper::convertDateToLocale($hotel['checkin_date'], $quotation['language']) }}
                            -
                            {{ \App\Helpers\DateHelper::convertDateToLocale($hotel['checkout_date'], $quotation['language']) }}
                        </span></td>
                    <td>{{ $hotel['nights'] }}</td>
                    <td>
                        @foreach ($hotel['rooms_count'] as $room)
                            <span>{{ $hotel['room_category'] }} - {{ $room }}</span><br>
                        @endforeach
                    </td>
                    <td>{{ $hotel['hotel_category'] }}</td>
                    <td>{{ $hotel['meal_type'] }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>


<div class="mb-4 card border-top">
    <div class="text-center card-header">
        <h4 class="mb-0">{{ __('labels.itinerary') }} : {{ count($quotation['days']) }} {{ __('labels.days') }} /
            {{ count($quotation['days']) - 1 }} {{ __('labels.nights') }}</h4>
    </div>
    <div class="card-body">
        <ul class="timeline">
            @foreach ($quotation['days'] ?? [] as $day)
                @if (isset($day['day']))
                    <li class="mb-3 timeline-item">
                        <span class="timeline-point timeline-point-success"></span>
                        <div class="timeline-event">
                            <div class="mb-3 timeline-event-title">
                                @php $desList = ""; @endphp
                                @foreach ($day['attractions'] ?? [] as $attraction)
                                    @php
                                        if ($attraction['attraction_type'] == 'attraction') {
                                            $attrData = App\Models\Attraction::find($attraction['attraction-id']);
                                        } elseif ($attraction['attraction_type'] == 'city_tour') {
                                            $attrData = App\Models\CityTour::find($attraction['attraction-id']);
                                        } elseif ($attraction['attraction_type'] == 'excursion') {
                                            $attrData = App\Models\Excursion::find($attraction['attraction-id']);
                                        }
                                        if (isset($quotation['language']) && $quotation['language'] != 'en') {
                                            $name_key = $quotation['language'] . '_name';
                                            $desList .= $attrData->$name_key . '<i class="bx bx-right-arrow-alt"></i>';
                                        } else {
                                            $desList .= $attrData->name . '<i class="bx bx-right-arrow-alt"></i>';
                                        }
                                    @endphp
                                @endforeach
                                <strong class="h5">{{ __('labels.day') }} {{ $day['day'] }}</strong><br>
                                <strong><span
                                        class="text-muted">{{ \App\Helpers\DateHelper::convertDateToLocale(date('M d, Y', strtotime($day['date'])), $quotation['language'], false) }}</span></strong>
                                <h5 data-day-id="{{ $day['day'] }}" class="timeline-event">
                                    @if ($day['day'] > 1)
                                        {{ \App\Models\Place::find($day['start'])->name ?? '' }} <i
                                            class="bx bx-right-arrow-alt"></i>
                                    @endif
                                    {!! $desList !!}
                                    {{ App\Models\Place::find($day['end'])->name ?? '' }} </br>
                                    <i class="h6">{{ __('index.labels.duration') }}:
                                        {{ __('index.labels.approximately') }} <span
                                            class="duration-text">{{ $quotation['summery']['duration'][$day['day']] ?? '00:00' }}</span>
                                        @if (!$privateLink)
                                            <small class="text-primary edit-duration" style="cursor: pointer;">[Edit
                                                Duration]</small>
                                        @endif
                                    </i>
                                </h5>
                            </div>

                            <p class="mb-2">
                                @if (isset($quotation['language']) && $quotation['language'] != 'en')
                                    @php $des_key = $quotation['language'] . '_description'; @endphp
                                    {!! App\Models\Place::find($day['end'])->$des_key ?? '' !!}
                                @else
                                    {!! App\Models\Place::find($day['end'])->description ?? '' !!}
                                @endif
                            </p>
                            @foreach ($day['attractions'] ?? [] as $attraction)
                                @php
                                    if ($attraction['attraction_type'] == 'attraction') {
                                        $attrData = App\Models\Attraction::find($attraction['attraction-id']);
                                    } elseif ($attraction['attraction_type'] == 'city_tour') {
                                        $attrData = App\Models\CityTour::find($attraction['attraction-id']);
                                    } elseif ($attraction['attraction_type'] == 'excursion') {
                                        $attrData = App\Models\Excursion::find($attraction['attraction-id']);
                                    }
                                @endphp
                                <div class="flex-wrap gap-2 mb-4 justify-content-between">
                                    <div class="flex-wrap d-flex align-items-center mb-50 row">
                                        <div class="mb-2 col-md-3 col-12">
                                            @if ($attrData->image)
                                                @if (filter_var($attrData->image, FILTER_VALIDATE_URL))
                                                    <img src="{{ $attrData->image }}"
                                                        alt="{{ __('labels.hotel_image') }}"
                                                        class="img-thumbnail square-cover-img" width="200">
                                                @else
                                                    <img src="{{ (new App\Models\Attraction())->getImageUrl($attrData->image) }}"
                                                        alt="Avatar" width="100%"
                                                        class="img-thumbnail square-cover-img">
                                                @endif
                                            @endif
                                        </div>
                                        <div class="col-md-9 col-12">
                                            @if (isset($quotation['language']) && $quotation['language'] != 'en')
                                                @php $name_key = $quotation['language'] . '_name'; @endphp
                                                <strong>{{ $attrData->$name_key }}</strong>
                                            @else
                                                <strong>{{ $attrData->name ?? '' }}</strong>
                                            @endif


                                            @if (isset($quotation['language']) && $quotation['language'] != 'en')
                                                @php $des_key = $quotation['language'] . '_description'; @endphp
                                                <p class="mb-0 fw-medium">{!! $attrData->$des_key ?? '' !!}</p>
                                            @else
                                                <p class="mb-0 fw-medium">{!! $attrData->description ?? '' !!}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </li>
                @endif
            @endforeach
        </ul>
    </div>
</div>


@if (!isset($isPdfMode) || !$isPdfMode)
    @include('quotation.partials.invoice', [
        'quotation' => $quotation,
        'rates' => $rates,
        'classes' => 'border-top',
        'showBreakDown' => $showBreakDown,
        'showSupplement' => true,
        'privateLink' => $privateLink,
    ])
@endif


<div id="accordionIncExc" class="mb-4 accordion">
    <!-- Notes Section -->
    <div class="mb-4 card accordion-item">
        <h2 class="accordion-header">
            <button class="accordion-button collapsed text-danger" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionIncExc-0">
                {{ __('labels.notes') }}
            </button>
        </h2>
        <div id="accordionIncExc-0" class="accordion-collapse collapse">
            <div class="accordion-body">
                <div class="accordion-wrapper" data-ul-id="0">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][0]))
                            {!! trim($quotation['summery']['policy'][0]) !!}
                        @else
                            <ul>
                                <li>{{ 'El tren está sujeto a disponibilidad. Y las actividades posteriores al viaje en tren están sujetas a la disponibilidad de tiempo, ya que el tren en la mayoría de las ocasiones no es puntual.' }}
                                <li> En caso de estar llenos estos hoteles en el momento de hacer la reserva, se
                                    ofrecerán
                                    otros de categoría similar.</li>
                                <li>El orden de las visitas puede cambiar.</li>
                                <li>Se aconseja hacer las compras en Kandy, Matale, Nuwara Eliya, Galle y Colombo.</li>
                                </li>
                            </ul>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Optional Tours and Activities -->
    <div class="mb-4 card accordion-item">
        <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionIncExc-6">
                {{ __('labels.optional_tours_activities') }}
            </button>
        </h2>
        <div id="accordionIncExc-6" class="accordion-collapse collapse">
            <div class="accordion-body">

                <!-- Around Habarana, Sigiriya, Dambulla -->
                <p><em><strong>{{ __('labels.around_habarana_sigiriya_dambulla') }}:</strong></em></p>
                <div class="accordion-wrapper" data-ul-id="6">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][6]))
                            <p>{!! trim($quotation['summery']['policy'][6]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li>{{ 'Excursión sobre el lomo del elefante en la jungla. [De pago] = 25 € por persona' }}
                                </li>
                                <li>{{ 'Excursión de aldea rural yendo en canoa en un lago, dando una pequeña vuelta en tuk-tuk y luego en carro tirado por bueyes. [De pago] = 20 € por persona' }}
                                </li>
                                <li>{{ 'Visita de un museo gemológico. Sri Lanka tiene una larga tradición de producción degemas. En este país se extraen rubíes, zaﬁros, amatistas, granates, entre otras. [Gratis]' }}
                                </li>
                                <li>{{ 'Visita a un establecimiento de telas tradicionales y seda con la oportunidad de vestir unsari o sarón (trajes tradicionales de Sri Lanka) y hacer una foto vestido como un local. [Gratis]' }}
                                </li>
                                <li>{{ 'Visita a un taller de madera artesanal. [Gratis]' }} </li>
                                <li>{{ 'Masajes Ayurvédicos.[De pago] = 35 € por persona' }} </li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>

                <!-- Around Kandy -->
                <p><em><strong>{{ __('labels.around_kandy') }}:</strong></em></p>
                <div class="accordion-wrapper" data-ul-id="7">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][7]))
                            <p>{!! trim($quotation['summery']['policy'][7]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li> {{ 'Excursión sobre el lomo del elefante en la jungla. [De pago] = 25 € por persona' }}
                                </li>
                                <li> {{ 'Visita de un museo gemológico. Sri Lanka tiene una larga tradición de producción degemas. En este país se extraen rubíes, zaﬁros, amatistas, granates, entre otras. [Gratis]' }}
                                </li>
                                <li> {{ 'Visita a un establecimiento de telas tradicionales y seda con la oportunidad de vestir unsari o sarón (trajes tradicionales de Sri Lanka) y hacer una foto vestido como un local. [Gratis]' }}
                                </li>
                                <li> {{ 'Visita a un taller de madera artesanal. [Gratis]' }} </li>
                                <li> {{ 'Masajes Ayurvédicos. [De pago] = 35 € por persona' }} </li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>

                <!-- Around Galle -->
                <p><em><strong>{{ __('labels.around_galle') }}:</strong></em></p>
                <div class="accordion-wrapper" data-ul-id="8">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][8]))
                            <p>{!! trim($quotation['summery']['policy'][8]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li> {{ 'Criadero de Tortugas Marinas. [De pago] = 10 € por persona' }} </li>
                                <li> {{ 'Excursión en barca por lago Koggala. [De pago] = 20 € por persona' }}
                                </li>
                                <li> {{ 'Visita a un taller de madera artesanal. [Gratis]' }} </li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- This Package Includes -->
    <div class="mb-4 card accordion-item">
        <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionIncExc-1">
                {{ __('labels.package_includes') }}
            </button>
        </h2>
        <div id="accordionIncExc-1" class="accordion-collapse collapse">
            <div class="accordion-body">
                <div class="accordion-wrapper" data-ul-id="1">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][1]))
                            <p>{!! trim($quotation['summery']['policy'][1]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li>Alojamiento en los Hoteles seleccionados. Si estos hoteles están completos en el
                                    momento
                                    de la reserva, se reservará un hotel de categoría y rango de precios similares.
                                </li>
                                <li>Transporte privado todo el circuito con chófer asistente con conocimientos de
                                    español
                                    atendiendo a la disponibilidad. En su defecto, chófer-asistente habla Inglesa
                                </li>
                                <li>Servicio de recogida y acompañamiento al aeropuerto a la llegada y salida del
                                    país.</li>
                                <li>El salario, el alojamiento y las comidas del conductor.</li>
                                <li>Gasolina, impuestos de circulación, aparcamiento y seguro.</li>
                                <li>Asistencia telefónica en español las 24 horas desde Sri Lanka.</li>
                                <li>Todas las entradas a monumentos, safari jeep 4×4 y templos del itinerario.</li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- This Package Excludes -->
    <div class="mb-4 card accordion-item">
        <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionIncExc-2">
                {{ __('labels.package_excludes') }}
            </button>
        </h2>
        <div id="accordionIncExc-2" class="accordion-collapse collapse">
            <div class="accordion-body">
                <div class="accordion-wrapper" data-ul-id="2">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][2]))
                            <p>{!! trim($quotation['summery']['policy'][2]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li>Los vuelos internacionales.</li>
                                <li>Los almuerzos.</li>
                                <li>Las excursiones y actividades opcionales.</li>
                                <li>Las propinas.</li>
                                <li>Los gastos del visado de entrada a Sri Lanka.</li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods and Cancellation Policy -->
    <div class="mb-4 card accordion-item">
        <h2 class="accordion-header">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionIncExc-3">
                {{ __('labels.payment_cancellation_policy') }}
            </button>
        </h2>
        <div id="accordionIncExc-3" class="accordion-collapse collapse">
            <div class="accordion-body">
                <p><em><strong>{{ __('labels.payment_methods') }}:</strong></em></p>
                <div class="accordion-wrapper" data-ul-id="3">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][3]))
                            <p>{!! trim($quotation['summery']['policy'][3]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li>La forma de pago es un 20% mediante transferencia bancaria a una c/c de España.
                                </li>
                                <li>El 80% restante se paga en metálico a la llegada a Sri Lanka o mediante
                                    transferencia bancaria internacional a Sri Lanka.</li>
                                <li>Nota: En caso de querer pagar mediante transferencia bancaria internacional,
                                    este
                                    importe se verá incrementado en un 5% por los gastos bancarios originados.</li>
                                <li>La reserva queda hecha desde el momento en que nos hagáis este depósito del 20%.
                                </li>
                                <li>Una vez hecho el depósito os enviaremos recibo por el importe del mismo y, al
                                    llegar
                                    a Sri Lanka y hacer efectivo el pago, recibiréis una factura por el importe
                                    total de
                                    nuestros servicios.</li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>

                <p><em><strong>{{ __('labels.cancellation_policy') }}:</strong></em></p>
                <div class="accordion-wrapper" data-ul-id="4">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][4]))
                            <p>{!! trim($quotation['summery']['policy'][4]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li>Hasta 30 días antes del viaje, se devuelve el 100% del depósito si hay
                                    cancelación
                                    del viaje.</li>
                                <li>Entre 29 días antes y 15 días antes del inicio del viaje hay una penalización
                                    del
                                    50% del importe del depósito.</li>
                                <li>Las cancelaciones en los 14 días anteriores al viaje no tienen devolución de
                                    depósito.</li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>

                <p><em><strong>{{ __('labels.note_on_cancellations') }}:</strong></em></p>
                <div class="accordion-wrapper" data-ul-id="5">
                    <div class='edit-content'>
                        @if (isset($quotation['summery']['policy'][5]))
                            <p>{!! trim($quotation['summery']['policy'][5]) !!}</p>
                        @else
                            <p>
                            <ul>
                                <li>Todas las cancelaciones tienen un cargo por gastos de administración de 90 €.
                                </li>
                            </ul>
                            </p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cooments section -->
    @if (!isset($isPdfMode) || !$isPdfMode)
        <div class="mb-4 card accordion-item active">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#accordionIncExc-7" aria-expanded="true">
                    {{ __('labels.conversation_history') }}
                </button>
            </h2>
            <div id="accordionIncExc-7" class="accordion-collapse collapse show">
                <div class="accordion-body">
                    <div class="accordion-wrapper" data-ul-id="2">
                        @include('quotation.content.comments.index', [
                            'quotation' => $quotation,
                            'privateLink' => $privateLink,
                        ])
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>


@if (!$email)
    <div class="col-12 d-flex justify-content-between">
        <button class="btn btn-outline-secondary btn-prev">
            <i class="bx bx-left-arrow-alt bx-sm ms-sm-n2 me-sm-2"></i>
            <span class="align-middle d-sm-inline-block d-none">Previous</span>
        </button>
        <button class="btn btn-success btn-outline-done">
            <i class="bx bx-save bx-sm ms-sm-n2 me-sm-2"></i>
            <span class="align-middle d-sm-inline-block d-none">Save</span>
        </button>
        <button class="btn btn-outline-secondary btn-next">
            <span class="align-middle d-sm-inline-block d-none me-sm-2">Next</span>
            <i class="bx bx-chevron-right bx-sm me-sm-n2"></i>
        </button>
    </div>
@endif
