@extends('layouts/contentNavbarLayout')

@section('title', 'Create New Booking')

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="fw-bold py-3 mb-4">
            <span class="text-muted fw-light">Bookings /</span> Create New Booking
        </h4>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <h5 class="card-header">Booking Details</h5>
                    <div class="card-body">
                        <form action="{{ route('bookings.store') }}" method="POST">
                            @csrf
                            <input type="hidden" name='created_from' value="office">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="template_id" class="form-label">Tour Template</label>
                                    <select id="template_id" name="template_id"
                                        class="form-select @error('template_id') is-invalid @enderror" required>
                                        <option value="">Select a template</option>
                                        @foreach ($templates as $template)
                                            <option value="{{ $template->id }}"
                                                {{ old('template_id') == $template->id ? 'selected' : '' }}>
                                                {{ $template->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('template_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="market_id" class="form-label">Market</label>
                                    <select id="market_id" name="market_id"
                                        class="form-select @error('market_id') is-invalid @enderror" required>
                                        <option value="">Select a market</option>
                                        @foreach ($markets as $market)
                                            <option value="{{ $market->id }}"
                                                {{ old('market_id') == $market->id ? 'selected' : '' }}>
                                                {{ $market->market }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('market_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="name" class="form-label">Tour Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                        id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="full_name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control @error('full_name') is-invalid @enderror"
                                        id="full_name" name="full_name" value="{{ old('full_name') }}" required>
                                    @error('full_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                        id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                        id="phone" name="phone" value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="pax" class="form-label">Number of Participants</label>
                                    <input type="number" class="form-control @error('pax') is-invalid @enderror"
                                        id="pax" name="pax" value="{{ old('pax') }}" min="1" required>
                                    @error('pax')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="4">{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="whatsapp_contact"
                                    name="whatsapp_contact" {{ old('whatsapp_contact') ? 'checked' : '' }}>
                                <label class="form-check-label" for="whatsapp_contact">
                                    Contact via WhatsApp
                                </label>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary me-2">Create Inquiry</button>
                                <a href="{{ route('bookings.index') }}" class="btn btn-outline-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
