@extends('layouts/blankLayoutEx')

@section('title', __('labels.view_quotation'))

@section('content')
    <div class="container-fuid">
        <div class="row invoice-preview">
            <div class="col-12">
                <div class="card">
                    <div class="pb-4 text-center card-header">
                        <div class="p-3 border shadow-sm rounded-top" style="border: 3px solid #fb5d4336;">
                            <div class="d-flex flex-column align-items-center">
                                <div class="mb-1 d-flex justify-content-center">
                                    <div class="flex-shrink-0">
                                        <span class="avatar-initial avatar-shadow-primary rounded-circle">
                                            <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('assets/img/branding/logo-s.jpeg'))) }}"
                                                alt="Logo" class="img-fluid" style="max-width: 250px;margin-top: 5px">
                                        </span>
                                    </div>
                                </div>
                                <p class="mb-0 text-center text-blue small">{{ __('labels.registered_office') }}: 38/01,
                                    Stone House Suite,
                                    Nittawela Road, Kandy, Sri Lanka</p>
                                <p class="mb-0 text-center text-blue small">{{ __('labels.operational_office') }}: No 242,
                                    Kandy Road,
                                    Kurunegala, Sri Lanka</p>
                                <p class="mb-0 text-center text-blue small">
                                    <EMAIL>,
                                    <EMAIL></p>
                                <p class="mb-0 text-center text-blue small"> +************, +************, +94 78615615, +94
                                    372201747</p>
                                <p class="mb-0 text-center text-blue strong">
                                    https://srilankaviajeseden.es</p>
                                <p class="mb-0 text-center small text-blue">{{ __('labels.registration_number') }}:
                                    SLTDA/SQA/TA/02238, {{ __('labels.business_registration') }}: PV 106460</p>
                            </div>
                        </div>
                    </div>
                    <div class="px-0 card-body px-md-4">
                        <h3 class="mb-12 text-center blue-heading" style="width: 98%">HOTEL CONFIRMATION REPORT -
                            {{ $quotation['reference_no'] }}</h3>
                        <h4 class="mb-3 text-center">{{ count($quotation['days']) }} {{ __('labels.days') }},
                            {{ count($quotation['days']) - 1 }} {{ __('labels.nights') }} in Sri Lanka</h4>
                        <div class="">
                            <table class="table mb-3 table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-white" style="width: 20%">{{ __('labels.hotel') }}</th>
                                        <th class="text-white" style="width: 30%">{{ __('labels.checkin_checkout') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.nights') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.room_category') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.hotel_category') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.meal_type') }}</th>
                                        <th class="text-white" style="width: 10%">{{ 'Status' }}</th>
                                    </tr>
                                </thead>

                                <tbody class="table-border-bottom-0">
                                    @foreach ($hotels as $hotel)
                                        <tr style="font-size: 11px; line-height: 1.2;">
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                <span style="font-weight: 500;">{{ $hotel['hotel_name'] }}</span><br>
                                                <small style="font-size: 9px;">{{ $hotel['city_name'] }}</small>
                                            </td>
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                <span style="white-space: nowrap;">{{ $hotel['checkin_date'] }} -
                                                    {{ $hotel['checkout_date'] }}</span>
                                            </td>
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                {{ $hotel['nights'] }}</td>
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                @foreach ($hotel['rooms_count'] as $room)
                                                    <span style="white-space: nowrap;">{{ $hotel['room_category'] }} -
                                                        {{ $room }}</span>
                                                    @if (!$loop->last)
                                                        <br>
                                                    @endif
                                                @endforeach
                                            </td>
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                {{ $hotel['hotel_category'] }}
                                            </td>
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                {{ $hotel['meal_type'] }}</td>
                                            <td
                                                style="background: white; color: #566a7f; text-align:center; padding: 4px; font-size: 10px;">
                                                {{ $hotelStatus[$hotel['hotel_id']] ?? 'Pending' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

@endsection
