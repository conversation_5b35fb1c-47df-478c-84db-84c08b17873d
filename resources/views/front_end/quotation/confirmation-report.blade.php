@extends('layouts/blankLayoutEx')

@section('title', __('labels.view_quotation'))

@section('content')
    <div class="container-fuid">
        <div class="row invoice-preview">
            <div class="col-12">
                <div class="card">
                    <div class="pb-1 text-center card-header">
                        <div class="p-2 border shadow-sm rounded-top" style="border: 3px solid #fb5d4336;">
                            <div class="d-flex flex-column align-items-center">
                                <div class="mb-0 d-flex justify-content-center">
                                    <div class="flex-shrink-0">
                                        <span class="avatar-initial avatar-shadow-primary rounded-circle">
                                            <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('assets/img/branding/logo-s.jpeg'))) }}"
                                                alt="Logo" class="img-fluid"
                                                style="max-width: 200px;margin-top: 0px;margin-bottom: 0px;">
                                        </span>
                                    </div>
                                </div>
                                <p class="mb-0 text-center text-blue small">{{ __('labels.registered_office') }}: 38/01,
                                    Stone House Suite,
                                    Nittawela Road, Kandy, Sri Lanka</p>
                                <p class="mb-0 text-center text-blue small">{{ __('labels.operational_office') }}: No 242,
                                    Kandy Road,
                                    Kurunegala, Sri Lanka</p>
                                <p class="mb-0 text-center text-blue small">
                                    <EMAIL>,
                                    <EMAIL></p>
                                <p class="mb-0 text-center text-blue small"> +************, +************, +94 78615615, +94
                                    372201747</p>
                                <p class="mb-0 text-center text-blue strong">
                                    https://srilankaviajeseden.es</p>
                                <p class="mb-0 text-center small text-blue">{{ __('labels.registration_number') }}:
                                    SLTDA/SQA/TA/02238, {{ __('labels.business_registration') }}: PV 106460</p>
                            </div>
                        </div>
                    </div>
                    <div class="px-0 card-body px-md-4">
                        <h3 class="mb-12 text-center blue-heading" style="width: 98%">HOTEL CONFIRMATION REPORT -
                            {{ $quotation['reference_no'] }}</h3>
                        <h4 class="mb-3 text-center">{{ count($quotation['days']) }} {{ __('labels.days') }},
                            {{ count($quotation['days']) - 1 }} {{ __('labels.nights') }} in Sri Lanka</h4>
                        <div class="">
                            <table class="table mb-3 table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-white" style="width: 20%">{{ __('labels.hotel') }}</th>
                                        <th class="text-white" style="width: 30%">{{ __('labels.checkin_checkout') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.nights') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.room_category') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.hotel_category') }}</th>
                                        <th class="text-white" style="width: 10%">{{ __('labels.meal_type') }}</th>
                                        <th class="text-white" style="width: 10%">{{ 'Status' }}</th>
                                    </tr>
                                </thead>

                                <tbody class="table-border-bottom-0">
                                    @foreach ($hotels as $hotel)
                                        <tr>
                                            <td class="hotel-name-cell">
                                                <strong>{{ $hotel['hotel_name'] }}</strong><br>
                                                <small>{{ $hotel['city_name'] }}</small>
                                            </td>
                                            <td class="date-cell">
                                                {{ $hotel['checkin_date'] }} - {{ $hotel['checkout_date'] }}
                                            </td>
                                            <td class="nights-cell">
                                                {{ $hotel['nights'] }}
                                            </td>
                                            <td class="room-cell">
                                                @foreach ($hotel['rooms_count'] as $room)
                                                    {{ $hotel['room_category'] }} - {{ $room }}@if (!$loop->last)
                                                        <br>
                                                    @endif
                                                @endforeach
                                            </td>
                                            <td class="category-cell">
                                                {{ $hotel['hotel_category'] }}
                                            </td>
                                            <td class="meal-cell">
                                                {{ $hotel['meal_type'] }}
                                            </td>
                                            <td class="status-cell">
                                                {{ $hotelStatus[$hotel['hotel_id']] ?? 'Pending' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

@endsection
