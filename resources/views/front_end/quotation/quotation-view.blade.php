@extends('layouts/blankLayout')

@section('title', __('labels.view_quotation'))

@section('vendor-style')

@endsection

@section('vendor-script')

@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/chat.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/quotation.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/css/sScrollBar.css') }}">
    <style>
        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -8px rgba(var(--bs-primary-rgb), 0.75) !important;
        }
    </style>
@endsection

@section('content')
    @if (!isset($isPdfMode) || !$isPdfMode)
        @auth
            <div class="position-fixed" style="top: 20px; left: 20px; z-index: 1050;">
                <a href="{{ route('dashboard') }}" class="btn btn-primary shadow-lg d-flex align-items-center">
                    <i class="bx bx-home-alt me-2"></i>
                    <span>{{ __('labels.go_to_dashboard') }}</span>
                </a>
            </div>
        @endauth
    @endif

    <div class="container-xxl">
        <div class="row invoice-preview">
            <div class="col-12">
                <div class="card">
                    <div class="pb-4 text-center card-header">
                        <div class="p-3 border shadow-sm rounded-top" style="border: 3px solid #fb5d4336;">
                            <div class="d-flex flex-column align-items-center">
                                <div class="mb-3 d-flex justify-content-center" style="text-align: center; margin: auto">
                                    <div class="flex-shrink-0" style="text-align: center; margin: auto">
                                        <span class="avatar-initial avatar-shadow-primary rounded-circle"
                                            style="text-align: center; margin: auto">
                                            <img src="{{ asset('assets/img/branding/' . $quotation['language'] . '_logo.png') }}"
                                                alt="Logo" class="img-fluid"
                                                style="max-width: 250px;text-align: center; margin: auto">
                                        </span>
                                    </div>
                                </div>
                                <p class="mb-0 text-center text-blue small">{{ __('labels.registered_office') }}: 38/01,
                                    Stone House Suite,
                                    Nittawela Road, Kandy, Sri Lanka</p>
                                <p class="mb-0 text-center text-blue small">{{ __('labels.operational_office') }}: No 242,
                                    Kandy Road,
                                    Kurunegala, Sri Lanka</p>
                                <p class="mb-0 text-center text-blue small">
                                    <EMAIL>,
                                    <EMAIL></p>
                                <p class="mb-0 text-center text-blue small"> +************, +************, +94 78615615, +94
                                    372201747</p>
                                <p class="mb-0 text-center text-blue strong">
                                    https://srilankaviajeseden.es</p>
                                <p class="mb-0 text-center small text-blue">{{ __('labels.registration_number') }}:
                                    SLTDA/SQA/TA/02238, {{ __('labels.business_registration') }}: PV 106460</p>
                            </div>
                            <div class="mt-3 text-start position-relative">
                                <strong class="mb-1 d-block"><span
                                        class="text-heading fw-medium">{{ __('labels.reference_no') }}:
                                    </span>{{ $quotation['reference_no'] }}</strong>
                                <small class="mb-1 d-block"><span
                                        class="text-heading fw-medium">{{ __('labels.created_on') }}:
                                    </span>{{ $quotation['created_at']->format('M d,') }} <span
                                        id="orderYear">{{ $quotation['created_at']->format('Y') }}</span></small>
                                <small class="mb-1 d-block"><span
                                        class="text-heading fw-medium">{{ __('labels.created_by') }}:
                                    </span>{{ $quotation['user']->name }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="px-0 card-body border-top px-md-4">
                        @if ($quotation)
                            @include('quotation.content.partials.basic', [
                                'quotation' => $quotation,
                                'dates' => $dates,
                                'hotels' => $hotels,
                                'rates' => $rates,
                            ])
                        @endif
                    </div>

                    <div class="px-0 card-body border-top px-md-4">
                        @if (isset($rates) && !empty($rates))
                            @include('quotation.content.summery', [
                                'quotation' => $quotation,
                                'hotels' => $hotels,
                                'rates' => $rates,
                                'email' => true,
                                'showBreakDown' => $showBreakDown,
                                'runningJob' => $runningJob,
                                'privateLink' => $privateLink,
                            ])
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/quotation.js') }}"></script>
    <script src="{{ asset('assets/frontend/js/simpleScrollBar.js') }}"></script>

    <script>
        var hotel_meals = {!! json_encode(config('custom.hotel_meals')) !!};
        $(document).ready(function() {
            $(document).on('click', '.addCommentForm-btn', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();

                const $button = $(this);
                const originalText = $button.html();

                // Start loading state
                $button.prop('disabled', true);
                $button.html('<i class="bx bx-loader-alt bx-spin me-1"></i>Adding Comment...');

                const commentText = $('#commentText').val().trim();
                const commentId = parseInt($('#commentID').val().trim(), 10);
                const quotationId = $('#quotation_id').val();

                if (commentText === '') {
                    alert('Please enter a comment.');
                    // Reset button state
                    $button.prop('disabled', false);
                    $button.html(originalText);
                    NProgress.done();
                    return;
                }

                $.ajax({
                    url: '{{ route('quotation.saveCommentToDatabase') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        quotation_id: quotationId,
                        comment_id: commentId,
                        comment: commentText
                    },
                    success: function(response) {
                        if (response.success) {
                            let createdAt = new Date(response.comment.created_at);
                            let formattedDate = createdAt.toLocaleString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                                month: 'short',
                                day: '2-digit',
                                year: 'numeric'
                            });

                            let newComment = `
                      <li class="chat-message chat-message-right">
                          <div class="overflow-hidden d-flex">
                              <div class="chat-message-wrapper flex-grow-1">
                                  <div class="chat-message-text">
                                      <h6><strong>${response.comment.user_name}:</strong></h6>
                                      <p class="mb-0">${response.comment.comment}</p>
                                  </div>
                                  <div class="mt-1 text-muted">
                                      <small>${formattedDate}</small>
                                  </div>
                              </div>
                          </div>
                      </li>
                  `;

                            $("#commentID").val(commentId + 1);
                            $('#commentText').val(''); // Clear input field
                            $('.chat-history-body ul').append(newComment); // Append new comment
                            NProgress.done();
                        } else {
                            alert('Failed to add comment.');
                        }

                        // Reset button state
                        $button.prop('disabled', false);
                        $button.html(originalText);
                    },
                    error: function() {
                        alert('An error occurred while adding the comment.');
                        NProgress.done();

                        // Reset button state
                        $button.prop('disabled', false);
                        $button.html(originalText);
                    }
                });
            });

            const scrollContainer = $('.invoice-preview.table-responsive');

            $('.scroll-left').on('click', function() {
                scrollContainer.animate({
                    scrollLeft: '-=100'
                }, 300); // Scroll left
            });

            $('.scroll-right').on('click', function() {
                scrollContainer.animate({
                    scrollLeft: '+=100'
                }, 300); // Scroll right
            });

            $(".horizontal-container").sScrollBar({
                scrollWidth: 10,
                borderRadius: 5,
                railBgColor: "#c9eded",
                handleBgColor: "#3dbab5",
                railDefaultOpacity: 0.9,
                handleDefaultOpacity: 0.7,
                showArrows: true,
                clickScrollSpeed: 200,
            });
        });
    </script>
@endsection
