@extends('layouts/blankLayout')

@section('title', 'Register Basic - Pages')

@section('page-style')
    <!-- Page -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/css/pages/page-auth.css') }}">
@endsection


@section('content')
    <div class="authentication-wrapper authentication-cover">
        <!-- Logo -->
        <!--a href="/" class="gap-2 auth-cover-brand d-flex align-items-center">
                    <img src="{{ asset('assets/img/branding/logo.png') }}" alt="" width="150px">
                </a-->
        <!-- /Logo -->
        <div class="m-0 authentication-inner row">

            <!-- /Left Text -->
            <div class="p-0 m-0 d-none d-lg-flex col-lg-7 col-xl-8 align-items-center">
                <div class="d-flex justify-content-center" style="width: 100%; height: 100%">
                    <img src="{{ asset('assets/img/login-cover.jpeg') }}" class="img-fluid" alt="Login image"
                        style="    display: block;width: 100vw;height: 100vh;object-fit: cover;"
                        data-app-dark-img="illustrations/boy-with-rocket-dark.png"
                        data-app-light-img="illustrations/boy-with-rocket-light.png">
                </div>
            </div>
            <!-- /Left Text -->

            <!-- Register -->
            <div class="p-6 d-flex col-12 col-lg-5 col-xl-4 align-items-center authentication-bg p-sm-12">
                <div class="pt-5 mx-auto mt-12 w-px-400">
                    <h4 class="mb-1">Adventure starts here 🚀</h4>
                    <p class="mb-3">Make your app management easy and fun!</p>

                    <form id="formAuthentication" class="mb-3 fv-plugins-bootstrap5 fv-plugins-framework"
                        action="{{ route('store') }}" method="POST" novalidate="novalidate">
                        @csrf
                        <div class="mb-3 fv-plugins-icon-container">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name"
                                placeholder="Enter your name" autofocus="">
                            @error('name')
                                <div
                                    class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="mb-3 fv-plugins-icon-container">
                            <label for="email" class="form-label">Email</label>
                            <input type="text" class="form-control" id="email" name="email"
                                placeholder="Enter your email">
                            @error('email')
                                <div
                                    class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="mb-3 form-password-toggle fv-plugins-icon-container">
                            <label class="form-label" for="password">Password</label>
                            <div class="input-group input-group-merge has-validation">
                                <input type="password" id="password" class="form-control" name="password"
                                    placeholder="············" aria-describedby="password">
                                <span class="cursor-pointer input-group-text"><i class="bx bx-hide"></i></span>
                            </div>
                            <div
                                class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                            </div>
                        </div>

                        <div class="mt-8 mb-3 fv-plugins-icon-container">
                            <div class="mb-8 form-check ms-2">
                                <input class="form-check-input" type="checkbox" id="terms-conditions" name="terms">
                                <label class="form-check-label" for="terms-conditions">
                                    I agree to
                                    <a href="javascript:void(0);">privacy policy &amp; terms</a>
                                </label>
                                <div
                                    class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary d-grid w-100">
                            Sign up
                        </button>
                        <input type="hidden">
                    </form>

                    <p class="text-center">
                        <span>Already have an account?</span>
                        <a href="{{ route('login') }}">
                            <span>Sign in instead</span>
                        </a>
                    </p>

                    <div class="my-6 divider">
                        <div class="divider-text">or</div>
                    </div>

                    <div class="d-flex justify-content-center">
                        <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-facebook me-1_5">
                            <i class="bx bxl-facebook-circle"></i>
                        </a>

                        <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-twitter me-1_5">
                            <i class="tf-icons bx bxl-twitter"></i>
                        </a>

                        <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-github me-1_5">
                            <i class="bx bxl-github"></i>
                        </a>

                        <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-google-plus">
                            <i class="tf-icons bx bxl-google"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- /Register -->
        </div>
    </div>
@endsection
