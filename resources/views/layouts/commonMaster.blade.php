<!DOCTYPE html>

<html class="light-style layout-menu-fixed" data-theme="theme-default" data-assets-path="{{ asset('/assets') . '/' }}"
    data-base-url="{{ url('/') }}" data-framework="laravel" data-template="vertical-menu-laravel-template-free">

<head>
    <meta charset="utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title>@yield('title') | Sri Lanka Viajes Eden </title>
    <meta name="description" content=@yield('description') />
    <meta name="keywords"
        content="{{ config('variables.templateKeyword') ? config('variables.templateKeyword') : '' }}">
    <!-- laravel CRUD token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Canonical SEO -->
    <link rel="canonical" href="{{ config('variables.productPage') ? config('variables.productPage') : '' }}">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/favicon/favicon.ico') }}" />



    <!-- Include Styles -->
    @include('layouts/sections/styles')

    <!-- Include Scripts for customizer, helper, analytics, config -->
    @include('layouts/sections/scriptsIncludes')

</head>

<body>


    <!-- Layout Content -->
    @yield('layoutContent')
    <!--/ Layout Content -->



    <!-- Include Scripts -->
    @include('layouts/sections/scripts')

    <script>
        $(document).ready(function() {
            let sidebar = $(".menu-inner");

            // Restore previous scroll position
            let savedScrollPosition = localStorage.getItem("sidebarScrollPosition");
            if (savedScrollPosition !== null) {
                sidebar.scrollTop(savedScrollPosition);
            }

            // Save scroll position before clicking a menu item
            $(".menu-inner a").on("click", function() {
                localStorage.setItem("sidebarScrollPosition", sidebar.scrollTop());
            });

            // Highlight active menu item
            let currentUrl = window.location.href;
            $(".menu-inner a").each(function() {
                if (this.href === currentUrl) {
                    $(this).closest(".menu-item").addClass("active");
                    $(this).parents(".menu-sub").show(); // Expand parent menu if nested
                    $(this).closest(".menu-item").parent().parent().addClass(
                        "open"); // Highlight parent menu
                }
            });
        });
    </script>

</body>

</html>
