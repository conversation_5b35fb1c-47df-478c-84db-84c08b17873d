@extends('layouts/contentNavbarLayout')

@section('title', 'User Profile')

@section('vendor-style')
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/searchpanes/2.3.2/css/searchPanes.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/select/2.0.5/css/select.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.dataTables.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/select/2.0.5/css/select.dataTables.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
@endsection

@section('vendor-script')
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.bootstrap5.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/dataTables.buttons.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.bootstrap5.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.colVis.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/data-table.css') }}">
@endsection

@section('content')
    <!-- Users List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="my-3 mb-0 card-title d-inline-block">All Users</h5>

            <button class="btn add-new btn-primary float-end" id="addNewUser" tabindex="0"
                aria-controls="DataTables_Table_0" type="button" data-bs-toggle="offcanvas"
                data-bs-target="#offcanvasAddUser"><span><i class="bx bx-plus bx-sm me-0 me-sm-2"></i><span
                        class="d-none d-sm-inline-block">Add New
                        User</span></span></button>
        </div>
        <div class="card-datatable table-responsive">
            <table class="table datatables-users border-top">
                <thead>
                    <tr>
                        <th>Id</th>
                        <th>FirstName</th>
                        <th>LastName</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Status</th>
                        <th>Role</th>
                        <th>Actions</th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- Offcanvas to add new user -->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddUser" aria-labelledby="offcanvasAddUserLabel">
            <div class="offcanvas-header border-bottom">
                <h5 id="offcanvasAddUserLabel" class="offcanvas-title">Add User</h5>
                <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">

            </div>
        </div>

        <!-- Offcanvas to add edit user -->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEditUser" aria-labelledby="offcanvasEditUserLabel">
            <div class="offcanvas-header border-bottom">
                <h5 id="offcanvasAddUserLabel" class="offcanvas-title">Edit User</h5>
                <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">

            </div>
        </div>
    </div>

@endsection

@section('page-script')
    <script>
        $(document).ready(function() {

            $('.datatables-users').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('profile.getUsers') }}", // The route to fetch users via AJAX
                columns: [{
                        data: 'id',
                        name: 'Id'
                    },
                    {
                        data: 'firstname',
                        name: 'FirstName'
                    },
                    {
                        data: 'lastname',
                        name: 'LastName'
                    },
                    {
                        data: 'email',
                        name: 'Email'
                    },
                    {
                        data: 'phone',
                        name: 'Phone'
                    },
                    {
                        data: 'status',
                        name: 'Status'
                    },
                    {
                        data: 'role',
                        name: 'Role'
                    },
                    {
                        data: null, // We use null because we are rendering custom content
                        name: 'Action',
                        orderable: false, // Disable sorting on the action column
                        searchable: false, // Disable searching on the action column
                        render: function(data, type, row) {
                            return `
                                <button type="button" class="btn btn-icon delete-record text-danger me-2" data-id="${row.id}" data-name="${row.firstname}">
                                    <i class="bx bx-trash"></i>
                                </button>
                                <button class="btn btn-icon edit-record text-warning editNewUser" data-id="${row.id}" title="Edit" data-bs-toggle="offcanvas" data-bs-target="#offcanvasEditUser">
                                    <i class="bx bx-edit"></i>
                                </button>
                            `;
                        }
                    }
                ],
                pageLength: 10, // Records per page
                dom: "<'row'<'col-sm-12 col-md-6 my-3'B><'col-sm-12 col-md-6 my-3'f>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-12 col-md-5'l><'col-sm-12 col-md-7'p>>",
                lengthMenu: [
                    [10, 25, 50, 100, -1], // Values for the dropdown
                    ['10 rows', '25 rows', '50 rows', '100 rows', 'Show all'] // Text for the dropdown
                ],
                buttons: [{
                        extend: 'copyHtml5',
                        text: '<span><i class="bx bx-copy me-2 bx-sm"></i>Copy</span>',
                        className: 'btn btn-label-secondary'
                    },
                    {
                        extend: 'excelHtml5',
                        text: '<span><i class="bx bx-file me-2 bx-sm"></i>Excel</span>',
                        className: 'btn btn-label-secondary'
                    },
                    {
                        extend: 'pdfHtml5',
                        text: '<span><i class="bx bx-file me-2 bx-sm"></i>PDF</span>',
                        className: 'btn btn-label-secondary'
                    },
                    {
                        extend: 'colvis',
                        text: '<span><i class="bx bx-columns me-2 bx-sm"></i>Column Visibility</span>',
                        className: 'btn btn-label-secondary'
                    }
                ],
                pagingType: "full_numbers", // Type of pagination
                language: {
                    paginate: {
                        previous: '<i class="bx bx-chevron-left bx-18px"></i>',
                        next: '<i class="bx bx-chevron-right bx-18px"></i>',
                        first: '<i class="bx bx-chevrons-left bx-18px"></i>',
                        last: '<i class="bx bx-chevrons-right bx-18px"></i>'
                    },
                    search: "Find:", // Custom text for search box
                    lengthMenu: "Show _MENU_ entries", // Custom text for length menu
                    info: "Displaying _START_ to _END_ of _TOTAL_ entries" // Custom info text
                }
            });

            $(document).on('click', '.editNewUser', function() {
                // Get the ID from the data attribute
                var userId = $(this).data('id');

                // Define the URL to fetch the user data
                var url = '/admin/profile/edit/' + userId; // Change this URL to your actual route

                // Make the AJAX request using the common function
                fetchDataWithAxios(url, {}, "GET", function(err, data) {
                    if (err) {
                        console.error('Error fetching data:', err);
                        alert('An error occurred while fetching user data.');
                    } else {
                        $("#offcanvasEditUser .offcanvas-body").html(data);
                    }
                });
            });

            $(document).on('click', '#addNewUser', function() {
                // Get the ID from the data attribute

                var url = '/admin/profile/create'; // Change this URL to your actual route

                // Make the AJAX request using the common function
                fetchDataWithAxios(url, {}, "GET", function(err, data) {
                    if (err) {
                        console.error('Error fetching data:', err);
                        alert('An error occurred while fetching user data.');
                    } else {
                        $("#offcanvasAddUser .offcanvas-body").html(data);
                    }
                });
            });

            $(document).on('click', '.delete-record', function(e) {
                e.preventDefault();

                var Id = $(this).data('id'); // Get the company ID
                var Name = $(this).data('name'); // Get the company name

                // SweetAlert2 confirmation dialog
                confirmDelete(Id, Name, function(Id) {
                    // AJAX request to delete the company
                    $.ajax({
                        url: '/admin/profile/' + Id,
                        method: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            // Show success message
                            Swal.fire(
                                'Deleted!',
                                `${Name} has been deleted.`,
                                'success'
                            ).then(() => {
                                $('.datatables-users').DataTable().ajax.reload(
                                    null, false); // False to retain pagination
                            });
                        },
                        error: function(error) {
                            // Show error message if deletion failed
                            Swal.fire(
                                'Error!',
                                'An error occurred while trying to delete the company.',
                                'error'
                            );
                        }
                    });
                });
            });
        });

        @if ($errors->any() || session('showModalAddUser'))
            console.log(123123);
            //$('#addNewUser').trigger('click');
        @endif

        @if ($errors->any() || session('showModalEditUser'))
            //$('.editNewUser[data-id="' + {{ old('id') }} + '"]').trigger('click');
        @endif
    </script>
@endsection
