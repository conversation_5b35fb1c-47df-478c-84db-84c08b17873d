<form class="add-new-user pt-0" id="createUserForm" action="{{ route('profile.store') }}" method="POST"
    enctype="multipart/form-data">
    @csrf

    <div class="mb-6">
        <label class="form-label" for="firstname">First Name</label>
        <input type="text" id="firstname" name="firstname" class="form-control" placeholder="John"
            value="{{ old('firstname') }}">
        @error('firstname')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="lastname">Last Name</label>
        <input type="text" id="lastname" name="lastname" class="form-control" placeholder="Doe"
            value="{{ old('lastname') }}">
        @error('lastname')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="email">Email</label>
        <input type="text" id="email" name="email" class="form-control" placeholder="<EMAIL>">
        @error('email')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="address">Address</label>
        <textarea type="text" id="address" name="address" class="form-control" placeholder="Colombo 07">{{ old('address') }}</textarea>
        @error('address')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="status">Status</label>
        <select id="status" name="status" class="form-select">
            @foreach (config('custom.user.status') as $key => $status)
                <option value="{{ $key }}" {{ old('status') == $key ? 'selected' : '' }}>
                    {{ $status }}
                </option>
            @endforeach
        </select>
    </div>

    <div class="mb-6">
        <label class="form-label" for="password">Password</label>
        <input type="password" id="password" name="password" class="form-control">
        @error('password')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="password_confirmation">Confirm Password</label>
        <input type="password" id="password_confirmation" name="password_confirmation" class="form-control">
    </div>

    <div class="mb-6">
        <label class="form-label" for="roles">Assign Role</label>
        <select id="roles" name="roles[]" class="form-select">
            @foreach ($roles as $role)
                <option value="{{ $role->name }}">{{ $role->name }}</option>
            @endforeach
        </select>
    </div>

    <div class="mb-6" id="markets-section" style="display: none;">
        <label class="form-label" for="markets">Assign Markets (Admin Only)</label>
        <select id="markets" name="markets[]" class="form-select" multiple>
            @foreach ($markets as $market)
                <option value="{{ $market->id }}">{{ $market->market }}</option>
            @endforeach
        </select>
        <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple markets</small>
    </div>

    <div class="mb-6">
        <label class="form-label" for="phone">Phone Number</label>
        <input type="text" id="phone" name="phone" class="form-control phone-number-mask"
            placeholder="************" value="{{ old('phone') }}">
        @error('phone')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label for="image" class="form-label">Profile Picture</label>
        <input class="form-control" type="file" id="image" name="image">
        @error('image')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label for="image-preview" class="form-label">Profile Picture Preview</label>
        <div class="avatar avatar-xl">
            <img src="https://dummyimage.com/300.png/09f/fff&text=Default" alt="Avatar" class="rounded"
                id="image-preview">
        </div>
    </div>

    <button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
    <button type="reset" class="btn btn-label-danger" data-bs-dismiss="modal">Cancel</button>
</form>

<script>
    $(document).ready(function() {
        // Initialize Select2 for markets
        $('#markets').select2({
            placeholder: 'Select markets...',
            allowClear: true,
            width: '100%'
        });

        // Function to toggle markets section
        function toggleMarketsSection() {
            const selectedRole = $('#roles').val();
            if (selectedRole && selectedRole.includes('Admin')) {
                $('#markets-section').show();
            } else {
                $('#markets-section').hide();
                $('#markets').val(null).trigger('change'); // Clear selection
            }
        }

        // Initial check
        toggleMarketsSection();

        // Listen for role changes
        $(document).on('change', '#roles', function() {
            toggleMarketsSection();
        });
    });
</script>
