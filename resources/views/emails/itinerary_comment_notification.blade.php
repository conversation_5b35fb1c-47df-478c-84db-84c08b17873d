@extends('emails.layout')

@section('content')
    <div class="card" style="padding: 10px">
        <div class="card-header">
            <h5>New Comment Added in Itinerary</h5>
        </div>
        <div class="card-body">
            <p><strong>From:</strong> {{ $senderName }}</p>

            @if ($quotationNo)
                <p><strong>Quotation Number:</strong> #{{ $quotationNo }}</p>
            @endif

            @if ($referenceId)
                <p><strong>Reference ID:</strong> {{ $referenceId }}</p>
            @endif

            <div class="message-content mt-4">
                <h6>Comment:</h6>
                <div class="p-3 bg-light rounded">
                    {!! nl2br(e($comment->comment)) !!}
                </div>
            </div>

            @if (!empty($comment->images) || !empty($comment->attachments))
                <div class="mt-3">
                    <p><strong>Attachments:</strong> This comment includes attachments. Please check the system to view
                        them.</p>
                </div>
            @endif

            <div class="mt-4">
                <a href="{{ route('quotations.show', $quotation['token']) }}" class="btn btn-primary">
                    View Quotation
                </a>
            </div>
        </div>
    </div>
@endsection
