@extends('emails.layout')

@section('content')
    <div class="card" style="padding: 10px">
        <div class="card-header">
            <h5>New Comment Added in task card </h5>
        </div>
        <div class="card-body">
            <p><strong>From:</strong> {{ $senderName }}</p>

            @if (!empty($quotationId))
                <p><strong>Quotation No:</strong> #{{ $quotationId }}</p>
            @endif

            @if ($referenceId)
                <p><strong>Reference ID:</strong> {{ $referenceId }}</p>
            @endif

            @if ($bookingId)
                <p><strong>Booking ID:</strong> {{ $bookingId }}</p>
            @endif

            <div class="message-content mt-4">
                <h6>Comment:</h6>
                <div class="p-3 bg-light rounded">
                    {!! nl2br(e($conversation->description)) !!}
                </div>
            </div>

            @if (!empty($conversation->images) || !empty($conversation->attachments))
                <div class="mt-3">
                    <p><strong>Attachments:</strong> This conversation includes attachments. Please check the system to view
                        them.</p>
                </div>
            @endif

            <div class="mt-4">
                <a href="{{ route('running_jobs.runningJobs.history', $runningJob->id) }}" class="btn btn-primary">
                    View Comment
                </a>
            </div>
        </div>
    </div>
@endsection
